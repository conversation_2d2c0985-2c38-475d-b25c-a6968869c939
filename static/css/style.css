/* 全局样式 */
body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden;
}

.container-fluid {
    height: 100vh;
    padding: 0;
}

.row {
    height: 100vh;
    margin: 0;
}

/* 侧边栏样式 */
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar-header h4 {
    font-weight: bold;
    margin-bottom: 5px;
}

.sidebar-header p {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 行业选择样式 */
.industry-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.industry-card {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.industry-card:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.industry-card.selected {
    background: rgba(255,255,255,0.3);
    border-color: #fff;
}

.industry-card i {
    font-size: 1.5rem;
    margin-bottom: 8px;
    display: block;
}

.industry-card .industry-name {
    font-size: 0.85rem;
    font-weight: 500;
}

/* 文件上传样式 */
.upload-area {
    border: 2px dashed rgba(255,255,255,0.3);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: rgba(255,255,255,0.6);
    background: rgba(255,255,255,0.1);
}

.upload-area.dragover {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.upload-area i {
    font-size: 2rem;
    margin-bottom: 10px;
    opacity: 0.7;
}

.upload-area p {
    margin: 5px 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

.upload-area small {
    display: block;
    margin-top: 5px;
    opacity: 0.6;
}

.supported-formats {
    font-size: 0.8rem;
}

.upload-progress {
    margin-top: 10px;
}

.upload-progress .progress {
    height: 4px;
    margin-bottom: 5px;
}

/* 文件分析结果样式 */
.file-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.analysis-summary {
    background: #e3f2fd;
    border-radius: 8px;
    padding: 15px;
}

.business-plan-analysis {
    background: #f3e5f5;
    border-radius: 8px;
    padding: 15px;
}

.content-preview-box {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
    padding: 15px;
}

.content-preview-box pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 0.85rem;
    line-height: 1.4;
}

.format-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin: 2px;
}

.format-badge.available {
    background: #d4edda;
    color: #155724;
}

.format-badge.unavailable {
    background: #f8d7da;
    color: #721c24;
}

.section-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.section-indicator.complete {
    background: #28a745;
}

.section-indicator.incomplete {
    background: #dc3545;
}

.section-indicator.partial {
    background: #ffc107;
}

/* 系统状态样式 */
.system-status {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 0.85rem;
    opacity: 0.8;
}

.stat-value {
    font-size: 0.85rem;
    font-weight: 500;
}

/* 主内容区域样式 */
.main-content {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: white;
}

/* 聊天头部样式 */
.chat-header {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.expert-info {
    display: flex;
    align-items: center;
}

.expert-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 15px;
}

.expert-details h5 {
    margin: 0;
    font-size: 1.1rem;
}

.expert-details p {
    margin: 0;
    font-size: 0.9rem;
}

.progress-bar {
    width: 200px;
}

/* 聊天消息区域样式 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.welcome-screen {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.features {
    margin: 30px 0;
}

.features i {
    display: block;
}

/* 消息样式 */
.message {
    margin-bottom: 20px;
    display: flex;
}

.message.user {
    justify-content: flex-end;
}

.message.assistant {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
}

.message.user .message-content {
    background: #007bff;
    color: white;
    border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
    background: white;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 4px;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.6;
    margin-top: 5px;
}

/* 输入区域样式 */
.chat-input {
    background: white;
    border-top: 1px solid #e9ecef;
    padding: 15px 20px;
}

.input-group {
    margin-bottom: 10px;
}

.quick-questions {
    margin-top: 10px;
}

.question-buttons {
    margin-top: 8px;
}

.question-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    padding: 6px 12px;
    margin: 3px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
}

.question-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

/* 加载动画 */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 18px;
    border-bottom-left-radius: 4px;
    max-width: 70%;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: #6c757d;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -100%;
        top: 0;
        width: 80%;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        width: 100%;
    }
    
    .industry-grid {
        grid-template-columns: 1fr;
    }
    
    .message-content {
        max-width: 85%;
    }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar,
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track,
.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

/* 报告样式 */
.report-section {
    margin-bottom: 25px;
}

.report-section h4 {
    color: #495057;
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
    margin-bottom: 15px;
}

.report-section p {
    line-height: 1.6;
    margin-bottom: 10px;
}

.report-section ul {
    padding-left: 20px;
}

.report-section li {
    margin-bottom: 5px;
}
