// 全局变量
let currentIndustry = null;
let isTyping = false;
let conversationStarted = false;
let analysisMode = null; // 'document' 或 'guided'
let guidedAnalysisData = null;
let currentQuestionIndex = 0;

// DOM元素
const industryList = document.getElementById('industry-list');
const chatMessages = document.getElementById('chat-messages');
const messageInput = document.getElementById('message-input');
const sendButton = document.getElementById('send-button');
const welcomeScreen = document.getElementById('welcome-screen');
const chatInput = document.getElementById('chat-input');
const expertInfo = document.getElementById('expert-info');
const progressContainer = document.getElementById('progress-container');
const quickQuestions = document.getElementById('quick-questions');
const questionButtons = document.getElementById('question-buttons');
const fileInput = document.getElementById('file-input');
const uploadArea = document.getElementById('upload-area');

// 新增DOM元素
const documentMode = document.getElementById('document-mode');
const guidedMode = document.getElementById('guided-mode');
const fileUploadSection = document.getElementById('file-upload-section');
const guidedQuestions = document.getElementById('guided-questions');
const guidedAnswer = document.getElementById('guided-answer');
const submitAnswerBtn = document.getElementById('submit-answer');
const currentQuestionTitle = document.getElementById('current-question-title');
const currentQuestionText = document.getElementById('current-question-text');
const questionHint = document.getElementById('question-hint');
const currentStageName = document.getElementById('current-stage-name');
const stageProgressBar = document.getElementById('stage-progress-bar');

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    loadIndustries();
    loadSystemStats();
    loadSupportedFormats();
    setupEventListeners();
});

// 加载行业列表
async function loadIndustries() {
    try {
        const response = await fetch('/industries');
        const data = await response.json();
        
        industryList.innerHTML = '';
        data.industries.forEach(industry => {
            const card = document.createElement('div');
            card.className = 'industry-card';
            card.dataset.industry = industry.id;
            card.innerHTML = `
                <i class="fas ${getIndustryIcon(industry.id)}"></i>
                <div class="industry-name">${industry.name}</div>
            `;
            card.addEventListener('click', () => selectIndustry(industry));
            industryList.appendChild(card);
        });
    } catch (error) {
        console.error('加载行业列表失败:', error);
    }
}

// 获取行业图标
function getIndustryIcon(industryId) {
    const icons = {
        'tech': 'fa-laptop-code',
        'finance': 'fa-coins',
        'healthcare': 'fa-heartbeat',
        'education': 'fa-graduation-cap',
        'retail': 'fa-shopping-cart',
        'manufacturing': 'fa-industry',
        'energy': 'fa-bolt',
        'agriculture': 'fa-seedling'
    };
    return icons[industryId] || 'fa-briefcase';
}

// 选择分析模式
function selectAnalysisMode(mode) {
    analysisMode = mode;

    // 更新UI
    document.querySelectorAll('.mode-option').forEach(option => {
        option.classList.remove('selected');
    });

    if (mode === 'document') {
        documentMode.classList.add('selected');
        fileUploadSection.style.display = 'block';
        guidedQuestions.style.display = 'none';
    } else if (mode === 'guided') {
        guidedMode.classList.add('selected');
        fileUploadSection.style.display = 'none';
        guidedQuestions.style.display = 'none'; // 等选择行业后再显示
    }

    console.log('选择分析模式:', mode);
}

// 选择行业
async function selectIndustry(industry) {
    // 更新UI
    document.querySelectorAll('.industry-card').forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelector(`[data-industry="${industry.id}"]`).classList.add('selected');

    currentIndustry = industry.id;

    // 检查分析模式
    if (!analysisMode) {
        alert('请先选择分析方式');
        return;
    }

    if (analysisMode === 'guided') {
        // 启动引导式分析
        await startGuidedAnalysis(industry.id);
    } else {
        // 原有的对话模式（现在用于文档分析的补充对话）
        await startDocumentAnalysisMode(industry.id);
    }
}

// 启动引导式分析
async function startGuidedAnalysis(industryId) {
    try {
        showTyping();
        const formData = new FormData();
        formData.append('industry', industryId);

        const response = await fetch('/guided-analysis/start', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        hideTyping();

        if (result.success) {
            // 隐藏欢迎屏幕，显示分析界面
            welcomeScreen.style.display = 'none';
            chatInput.style.display = 'none';
            expertInfo.style.display = 'flex';
            progressContainer.style.display = 'block';
            guidedQuestions.style.display = 'block';

            // 更新专家信息
            document.getElementById('expert-name').textContent = result.expert_name;
            document.getElementById('expert-description').textContent = result.expert_personality;

            // 显示欢迎消息
            addMessage('assistant', result.welcome_message);

            // 初始化引导式分析数据
            guidedAnalysisData = result;
            currentQuestionIndex = 0;

            // 显示第一个问题
            showCurrentQuestion();

            updateProgress(result.progress || 0);

        } else {
            alert('启动引导式分析失败: ' + result.error);
        }
    } catch (error) {
        hideTyping();
        console.error('启动引导式分析失败:', error);
        alert('启动引导式分析失败，请重试');
    }
}

// 启动文档分析模式
async function startDocumentAnalysisMode(industryId) {
    try {
        // 隐藏欢迎屏幕，显示文件上传界面
        welcomeScreen.style.display = 'none';
        expertInfo.style.display = 'flex';

        // 设置专家信息（简化版）
        const expertNames = {
            'tech': '小智',
            'finance': '小金',
            'healthcare': '小医'
        };

        document.getElementById('expert-name').textContent = expertNames[industryId] || '专家';
        document.getElementById('expert-description').textContent = '文档分析专家，为您解析商业计划书';

        // 显示提示消息
        addMessage('assistant', '您好！我是您的文档分析专家。请上传您的商业计划书，我将为您生成详细的分析报告。');

    } catch (error) {
        console.error('启动文档分析模式失败:', error);
        alert('启动文档分析模式失败，请重试');
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 分析模式选择
    documentMode.addEventListener('click', () => selectAnalysisMode('document'));
    guidedMode.addEventListener('click', () => selectAnalysisMode('guided'));

    // 发送消息
    sendButton.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // 引导式分析
    submitAnswerBtn.addEventListener('click', submitGuidedAnswer);
    guidedAnswer.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && e.ctrlKey) {
            e.preventDefault();
            submitGuidedAnswer();
        }
    });

    // 文件上传
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);
    fileInput.addEventListener('change', handleFileSelect);
}

// 发送消息
async function sendMessage() {
    const message = messageInput.value.trim();
    if (!message || isTyping) return;
    
    // 添加用户消息
    addMessage('user', message);
    messageInput.value = '';
    hideQuickQuestions();
    
    try {
        showTyping();
        const formData = new FormData();
        formData.append('message', message);
        formData.append('message_type', 'text');
        
        const response = await fetch('/chat/message', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        hideTyping();
        
        if (result.success) {
            // 添加助手回复
            addMessage('assistant', result.message);
            
            // 更新进度
            if (result.progress) {
                updateProgress(result.progress);
            }
            
            // 显示快速问题
            if (result.next_questions && result.next_questions.length > 0) {
                showQuickQuestions(result.next_questions);
            }
            
            // 如果有分析报告，显示报告
            if (result.analysis_report) {
                showAnalysisReport(result.analysis_report);
            }
            
        } else {
            addMessage('assistant', '抱歉，处理您的消息时出现了问题。请稍后再试。');
        }
    } catch (error) {
        hideTyping();
        console.error('发送消息失败:', error);
        addMessage('assistant', '网络连接出现问题，请检查网络后重试。');
    }
}

// 添加消息到聊天区域
function addMessage(role, content) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}`;
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = formatMessage(content);
    
    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = new Date().toLocaleTimeString();
    
    contentDiv.appendChild(timeDiv);
    messageDiv.appendChild(contentDiv);
    chatMessages.appendChild(messageDiv);
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 格式化消息内容
function formatMessage(content) {
    // 简单的Markdown支持
    return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>');
}

// 显示打字指示器
function showTyping() {
    if (isTyping) return;
    
    isTyping = true;
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message assistant';
    typingDiv.id = 'typing-indicator';
    
    typingDiv.innerHTML = `
        <div class="typing-indicator">
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
    `;
    
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 隐藏打字指示器
function hideTyping() {
    isTyping = false;
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// 显示快速问题
function showQuickQuestions(questions) {
    questionButtons.innerHTML = '';
    
    questions.forEach(question => {
        const button = document.createElement('span');
        button.className = 'question-btn';
        button.textContent = question;
        button.addEventListener('click', () => {
            messageInput.value = question;
            sendMessage();
        });
        questionButtons.appendChild(button);
    });
    
    quickQuestions.style.display = 'block';
}

// 隐藏快速问题
function hideQuickQuestions() {
    quickQuestions.style.display = 'none';
}

// 更新进度条
function updateProgress(progress) {
    const progressBar = document.getElementById('analysis-progress');
    progressBar.style.width = progress + '%';
    progressBar.setAttribute('aria-valuenow', progress);
}

// 显示分析报告
function showAnalysisReport(report) {
    const reportContent = document.getElementById('report-content');
    reportContent.innerHTML = formatReport(report.report_text);
    
    const reportModal = new bootstrap.Modal(document.getElementById('reportModal'));
    reportModal.show();
}

// 格式化报告内容
function formatReport(reportText) {
    // 简单的报告格式化
    return reportText
        .replace(/^(\d+\.\s.*?)$/gm, '<h4>$1</h4>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n\n/g, '</p><p>')
        .replace(/^/, '<p>')
        .replace(/$/, '</p>');
}

// 加载支持的文件格式
async function loadSupportedFormats() {
    try {
        const response = await fetch('/upload/supported-formats');
        const data = await response.json();

        const formatInfo = document.getElementById('format-info');
        const availableFormats = [];
        const unavailableFormats = [];

        for (const [mimeType, info] of Object.entries(data.supported_types)) {
            if (info.available) {
                availableFormats.push(info.name);
            } else {
                unavailableFormats.push(info.name);
            }
        }

        let infoText = `支持: ${availableFormats.join(', ')}`;
        if (unavailableFormats.length > 0) {
            infoText += ` | 需要依赖: ${unavailableFormats.join(', ')}`;
        }

        formatInfo.textContent = infoText;

    } catch (error) {
        console.error('加载支持格式失败:', error);
        document.getElementById('format-info').textContent = '格式信息加载失败';
    }
}

// 文件处理函数
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

// 显示当前问题
function showCurrentQuestion() {
    if (!guidedAnalysisData || !guidedAnalysisData.questions) return;

    const questions = guidedAnalysisData.questions;
    if (currentQuestionIndex >= questions.length) return;

    const question = questions[currentQuestionIndex];

    currentQuestionTitle.textContent = `问题 ${currentQuestionIndex + 1}/${questions.length}`;
    currentQuestionText.textContent = question.question;
    questionHint.textContent = question.placeholder || '';
    guidedAnswer.value = '';
    guidedAnswer.focus();

    // 更新阶段信息
    currentStageName.textContent = formatStageName(guidedAnalysisData.current_stage);

    // 更新进度
    const progress = ((currentQuestionIndex) / questions.length) * 80; // 80%用于问答
    stageProgressBar.style.width = progress + '%';
}

// 提交引导式分析答案
async function submitGuidedAnswer() {
    const answer = guidedAnswer.value.trim();
    if (!answer) {
        alert('请先回答问题');
        return;
    }

    if (!guidedAnalysisData || !guidedAnalysisData.questions) return;

    const question = guidedAnalysisData.questions[currentQuestionIndex];

    try {
        // 显示用户的回答
        addMessage('user', answer);

        showTyping();
        const formData = new FormData();
        formData.append('answer', answer);
        formData.append('question_id', question.id);

        const response = await fetch('/guided-analysis/answer', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        hideTyping();

        if (result.success) {
            // 显示专家回复
            addMessage('assistant', result.message);

            // 更新进度
            updateProgress(result.progress);

            if (result.analysis_completed) {
                // 分析完成，显示报告
                guidedQuestions.style.display = 'none';
                showGuidedAnalysisReport(result.analysis_report);
            } else {
                // 继续下一个问题
                if (result.stage_completed) {
                    addMessage('assistant', `太好了！我们已经完成了"${formatStageName(guidedAnalysisData.current_stage)}"部分的问题。`);
                }

                // 更新问题数据
                guidedAnalysisData.current_stage = result.current_stage;
                guidedAnalysisData.questions = result.questions;
                currentQuestionIndex = 0;

                // 显示下一个问题
                setTimeout(() => {
                    showCurrentQuestion();
                }, 1000);
            }
        } else {
            addMessage('assistant', '处理您的回答时出现问题，请重试。');
        }

    } catch (error) {
        hideTyping();
        console.error('提交答案失败:', error);
        addMessage('assistant', '网络连接出现问题，请检查网络后重试。');
    }
}

// 显示引导式分析报告
function showGuidedAnalysisReport(report) {
    // 格式化报告内容
    let reportHtml = '<div class="guided-analysis-report">';

    for (const [sectionKey, sectionContent] of Object.entries(report.analysis_sections)) {
        const sectionTitle = formatSectionTitle(sectionKey);
        reportHtml += `
            <div class="report-section">
                <h4>${sectionTitle}</h4>
                <div class="section-content">${formatReportContent(sectionContent)}</div>
            </div>
        `;
    }

    reportHtml += '</div>';

    // 显示报告
    const reportContent = document.getElementById('report-content');
    reportContent.innerHTML = reportHtml;

    const reportModal = new bootstrap.Modal(document.getElementById('reportModal'));
    reportModal.show();

    // 添加完成消息
    addMessage('assistant', '🎉 恭喜！您的引导式分析已完成。我已经为您生成了详细的分析报告，请查看报告了解专业建议。');
}

// 格式化阶段名称
function formatStageName(stage) {
    const stageNames = {
        'basic_info': '基本信息',
        'market_analysis': '市场分析',
        'business_model': '商业模式',
        'team_funding': '团队与资金',
        'technical_details': '技术细节',
        'compliance': '合规分析',
        'clinical': '临床分析'
    };
    return stageNames[stage] || stage;
}

// 格式化报告部分标题
function formatSectionTitle(sectionKey) {
    const titles = {
        'project_overview': '📋 项目概述',
        'market_analysis': '📊 市场分析',
        'business_model': '💼 商业模式',
        'team_funding': '👥 团队与资金',
        'technical_analysis': '🔧 技术分析',
        'compliance_analysis': '⚖️ 合规分析',
        'clinical_analysis': '🏥 临床分析',
        'comprehensive_assessment': '🎯 综合评估'
    };
    return titles[sectionKey] || sectionKey;
}

// 格式化报告内容
function formatReportContent(content) {
    return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n\n/g, '</p><p>')
        .replace(/^/, '<p>')
        .replace(/$/, '</p>')
        .replace(/^<p><\/p>$/, '');
}

// 上传文件
async function uploadFile(file) {
    const uploadProgress = document.getElementById('upload-progress');
    const uploadProgressBar = document.getElementById('upload-progress-bar');
    const uploadStatus = document.getElementById('upload-status');

    try {
        // 显示上传进度
        uploadProgress.style.display = 'block';
        uploadProgressBar.style.width = '0%';
        uploadStatus.textContent = '准备上传...';

        // 检查文件大小 (10MB限制)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            throw new Error('文件大小超过10MB限制');
        }

        const formData = new FormData();
        formData.append('file', file);

        // 检查分析类型
        const analysisType = document.querySelector('input[name="analysisType"]:checked')?.value || 'quick';
        const endpoint = analysisType === 'full' ? '/upload/analyze-document' : '/upload/business-plan';

        // 模拟上传进度
        uploadProgressBar.style.width = '30%';
        uploadStatus.textContent = '上传中...';

        const response = await fetch(endpoint, {
            method: 'POST',
            body: formData
        });

        uploadProgressBar.style.width = '80%';
        uploadStatus.textContent = '解析中...';

        const result = await response.json();

        uploadProgressBar.style.width = '100%';

        if (result.success) {
            uploadStatus.textContent = '上传成功！';

            if (result.analysis_report) {
                // 完整分析模式 - 直接显示分析报告
                addMessage('assistant', `文件"${file.name}"分析完成！我已经为您生成了详细的分析报告。`);
                showDocumentAnalysisReport(result.analysis_report);
            } else {
                // 快速分析模式 - 显示文件分析结果
                addMessage('assistant', `文件"${file.name}"上传并解析成功！让我为您展示分析结果...`);
                showFileAnalysisResult(result);
            }

            // 隐藏进度条
            setTimeout(() => {
                uploadProgress.style.display = 'none';
            }, 2000);

        } else {
            throw new Error(result.error || '上传失败');
        }

    } catch (error) {
        console.error('文件上传失败:', error);
        uploadStatus.textContent = '上传失败';
        uploadProgressBar.classList.add('bg-danger');

        let errorMessage = '文件上传失败：' + error.message;
        if (error.message.includes('413')) {
            errorMessage = '文件太大，请选择小于10MB的文件';
        } else if (error.message.includes('400')) {
            errorMessage = '不支持的文件格式或文件解析失败';
        }

        addMessage('assistant', errorMessage);

        // 隐藏进度条
        setTimeout(() => {
            uploadProgress.style.display = 'none';
            uploadProgressBar.classList.remove('bg-danger');
        }, 3000);
    }
}

// 显示文件分析结果
function showFileAnalysisResult(result) {
    // 填充文件基本信息
    const fileBasicInfo = document.getElementById('file-basic-info');
    fileBasicInfo.innerHTML = `
        <p><strong>文件名:</strong> ${result.file_info.name}</p>
        <p><strong>文件大小:</strong> ${formatFileSize(result.file_info.size)}</p>
        <p><strong>文件类型:</strong> ${result.file_info.type}</p>
        <p><strong>解析时间:</strong> ${new Date(result.file_info.parsed_at).toLocaleString()}</p>
    `;

    // 填充元数据
    const fileMetadata = document.getElementById('file-metadata');
    let metadataHtml = '';
    for (const [key, value] of Object.entries(result.metadata)) {
        if (value !== null && value !== undefined && value !== '') {
            metadataHtml += `<p><strong>${formatMetadataKey(key)}:</strong> ${value}</p>`;
        }
    }
    fileMetadata.innerHTML = metadataHtml || '<p class="text-muted">无额外元数据</p>';

    // 填充分析摘要
    const analysisSummaryContent = document.getElementById('analysis-summary-content');
    analysisSummaryContent.innerHTML = result.analysis_summary.replace(/\n/g, '<br>');

    // 填充创业计划书分析（如果有）
    if (result.business_plan_sections && Object.keys(result.business_plan_sections).length > 0) {
        document.getElementById('business-plan-analysis').style.display = 'block';

        const planSections = document.getElementById('plan-sections');
        let sectionsHtml = '<h6>检测到的部分:</h6>';
        for (const [section, content] of Object.entries(result.business_plan_sections)) {
            const hasContent = content && content.trim().length > 0;
            const indicator = hasContent ? 'complete' : 'incomplete';
            sectionsHtml += `
                <p>
                    <span class="section-indicator ${indicator}"></span>
                    ${formatSectionName(section)}
                    ${hasContent ? `(${content.length} 字符)` : '(缺失)'}
                </p>
            `;
        }
        planSections.innerHTML = sectionsHtml;

        const validationResults = document.getElementById('validation-results');
        const validation = result.validation_result;
        validationResults.innerHTML = `
            <h6>完整性评估:</h6>
            <p><strong>完整性评分:</strong> ${validation.completeness_score.toFixed(1)}%</p>
            <p><strong>状态:</strong> ${validation.is_complete ? '✅ 完整' : '⚠️ 不完整'}</p>
            ${validation.suggestions.length > 0 ?
                '<p><strong>建议:</strong></p><ul>' +
                validation.suggestions.map(s => `<li>${s}</li>`).join('') +
                '</ul>' : ''
            }
        `;
    }

    // 填充内容预览
    const contentPreview = document.getElementById('content-preview-text');
    contentPreview.textContent = result.content_preview;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('fileAnalysisModal'));
    modal.show();
}

// 显示文档分析报告
function showDocumentAnalysisReport(report) {
    let reportHtml = '<div class="document-analysis-report">';

    // 报告头部信息
    reportHtml += `
        <div class="report-header mb-4">
            <h5><i class="fas fa-file-alt"></i> 文档分析报告</h5>
            <p class="text-muted">
                <small>
                    文件: ${report.file_name} |
                    生成时间: ${new Date(report.generated_at).toLocaleString()} |
                    完整性评分: ${report.completeness_score.toFixed(1)}%
                </small>
            </p>
        </div>
    `;

    // 报告各部分
    for (const [sectionKey, sectionContent] of Object.entries(report.analysis_sections)) {
        const sectionTitle = formatDocumentSectionTitle(sectionKey);
        reportHtml += `
            <div class="report-section">
                <h4>${sectionTitle}</h4>
                <div class="section-content">${formatReportContent(sectionContent)}</div>
            </div>
        `;
    }

    // 改进建议
    if (report.recommendations && report.recommendations.length > 0) {
        reportHtml += `
            <div class="report-section">
                <h4>💡 改进建议</h4>
                <div class="section-content">
                    <ul>
                        ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;
    }

    reportHtml += '</div>';

    // 显示报告
    const reportContent = document.getElementById('report-content');
    reportContent.innerHTML = reportHtml;

    const reportModal = new bootstrap.Modal(document.getElementById('reportModal'));
    reportModal.show();
}

// 格式化文档分析部分标题
function formatDocumentSectionTitle(sectionKey) {
    const titles = {
        'project_overview': '📋 项目概述',
        'market_analysis': '📊 市场分析',
        'business_model': '💼 商业模式评估',
        'financial_analysis': '💰 财务分析',
        'team_analysis': '👥 团队分析',
        'risk_assessment': '⚠️ 风险评估',
        'development_suggestions': '🚀 发展建议',
        'overall_assessment': '🎯 总体评估'
    };
    return titles[sectionKey] || sectionKey;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化元数据键名
function formatMetadataKey(key) {
    const keyMap = {
        'format': '格式',
        'pages': '页数',
        'paragraphs': '段落数',
        'tables': '表格数',
        'slides': '幻灯片数',
        'sheets': '工作表',
        'encoding': '编码',
        'lines': '行数',
        'characters': '字符数',
        'title': '标题',
        'author': '作者',
        'subject': '主题',
        'created': '创建时间',
        'modified': '修改时间'
    };
    return keyMap[key] || key;
}

// 格式化部分名称
function formatSectionName(section) {
    const sectionMap = {
        'executive_summary': '执行摘要',
        'market_analysis': '市场分析',
        'business_model': '商业模式',
        'financial_projections': '财务预测',
        'team': '团队介绍',
        'risk_analysis': '风险分析',
        'implementation_plan': '实施计划'
    };
    return sectionMap[section] || section;
}

// 加载系统统计信息
async function loadSystemStats() {
    try {
        const response = await fetch('/system/stats');
        const stats = await response.json();
        
        document.getElementById('doc-count').textContent = 
            stats.vector_store_stats?.total_documents || '0';
        document.getElementById('system-status').textContent = 
            stats.is_initialized ? '就绪' : '初始化中';
    } catch (error) {
        console.error('加载系统统计失败:', error);
        document.getElementById('system-status').textContent = '错误';
    }
}
