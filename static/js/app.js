// 全局变量
let currentIndustry = null;
let isTyping = false;
let conversationStarted = false;

// DOM元素
const industryList = document.getElementById('industry-list');
const chatMessages = document.getElementById('chat-messages');
const messageInput = document.getElementById('message-input');
const sendButton = document.getElementById('send-button');
const welcomeScreen = document.getElementById('welcome-screen');
const chatInput = document.getElementById('chat-input');
const expertInfo = document.getElementById('expert-info');
const progressContainer = document.getElementById('progress-container');
const quickQuestions = document.getElementById('quick-questions');
const questionButtons = document.getElementById('question-buttons');
const fileInput = document.getElementById('file-input');
const uploadArea = document.getElementById('upload-area');

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    loadIndustries();
    loadSystemStats();
    loadSupportedFormats();
    setupEventListeners();
});

// 加载行业列表
async function loadIndustries() {
    try {
        const response = await fetch('/industries');
        const data = await response.json();
        
        industryList.innerHTML = '';
        data.industries.forEach(industry => {
            const card = document.createElement('div');
            card.className = 'industry-card';
            card.dataset.industry = industry.id;
            card.innerHTML = `
                <i class="fas ${getIndustryIcon(industry.id)}"></i>
                <div class="industry-name">${industry.name}</div>
            `;
            card.addEventListener('click', () => selectIndustry(industry));
            industryList.appendChild(card);
        });
    } catch (error) {
        console.error('加载行业列表失败:', error);
    }
}

// 获取行业图标
function getIndustryIcon(industryId) {
    const icons = {
        'tech': 'fa-laptop-code',
        'finance': 'fa-coins',
        'healthcare': 'fa-heartbeat',
        'education': 'fa-graduation-cap',
        'retail': 'fa-shopping-cart',
        'manufacturing': 'fa-industry',
        'energy': 'fa-bolt',
        'agriculture': 'fa-seedling'
    };
    return icons[industryId] || 'fa-briefcase';
}

// 选择行业
async function selectIndustry(industry) {
    // 更新UI
    document.querySelectorAll('.industry-card').forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelector(`[data-industry="${industry.id}"]`).classList.add('selected');
    
    currentIndustry = industry.id;
    
    // 开始对话
    try {
        showTyping();
        const formData = new FormData();
        formData.append('industry', industry.id);
        
        const response = await fetch('/chat/start', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        hideTyping();
        
        if (result.success) {
            // 隐藏欢迎屏幕，显示聊天界面
            welcomeScreen.style.display = 'none';
            chatInput.style.display = 'block';
            expertInfo.style.display = 'flex';
            progressContainer.style.display = 'block';
            
            // 更新专家信息
            document.getElementById('expert-name').textContent = result.expert_name;
            document.getElementById('expert-description').textContent = result.expert_personality;
            
            // 显示欢迎消息
            addMessage('assistant', result.welcome_message);
            
            // 显示初始问题
            if (result.initial_questions && result.initial_questions.length > 0) {
                showQuickQuestions(result.initial_questions);
            }
            
            // 启用输入
            messageInput.disabled = false;
            sendButton.disabled = false;
            messageInput.focus();
            
            conversationStarted = true;
            updateProgress(result.progress || 0);
            
        } else {
            alert('启动对话失败: ' + result.error);
        }
    } catch (error) {
        hideTyping();
        console.error('选择行业失败:', error);
        alert('选择行业失败，请重试');
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 发送消息
    sendButton.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    // 文件上传
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);
    fileInput.addEventListener('change', handleFileSelect);
}

// 发送消息
async function sendMessage() {
    const message = messageInput.value.trim();
    if (!message || isTyping) return;
    
    // 添加用户消息
    addMessage('user', message);
    messageInput.value = '';
    hideQuickQuestions();
    
    try {
        showTyping();
        const formData = new FormData();
        formData.append('message', message);
        formData.append('message_type', 'text');
        
        const response = await fetch('/chat/message', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        hideTyping();
        
        if (result.success) {
            // 添加助手回复
            addMessage('assistant', result.message);
            
            // 更新进度
            if (result.progress) {
                updateProgress(result.progress);
            }
            
            // 显示快速问题
            if (result.next_questions && result.next_questions.length > 0) {
                showQuickQuestions(result.next_questions);
            }
            
            // 如果有分析报告，显示报告
            if (result.analysis_report) {
                showAnalysisReport(result.analysis_report);
            }
            
        } else {
            addMessage('assistant', '抱歉，处理您的消息时出现了问题。请稍后再试。');
        }
    } catch (error) {
        hideTyping();
        console.error('发送消息失败:', error);
        addMessage('assistant', '网络连接出现问题，请检查网络后重试。');
    }
}

// 添加消息到聊天区域
function addMessage(role, content) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}`;
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = formatMessage(content);
    
    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = new Date().toLocaleTimeString();
    
    contentDiv.appendChild(timeDiv);
    messageDiv.appendChild(contentDiv);
    chatMessages.appendChild(messageDiv);
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 格式化消息内容
function formatMessage(content) {
    // 简单的Markdown支持
    return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>');
}

// 显示打字指示器
function showTyping() {
    if (isTyping) return;
    
    isTyping = true;
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message assistant';
    typingDiv.id = 'typing-indicator';
    
    typingDiv.innerHTML = `
        <div class="typing-indicator">
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
    `;
    
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 隐藏打字指示器
function hideTyping() {
    isTyping = false;
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// 显示快速问题
function showQuickQuestions(questions) {
    questionButtons.innerHTML = '';
    
    questions.forEach(question => {
        const button = document.createElement('span');
        button.className = 'question-btn';
        button.textContent = question;
        button.addEventListener('click', () => {
            messageInput.value = question;
            sendMessage();
        });
        questionButtons.appendChild(button);
    });
    
    quickQuestions.style.display = 'block';
}

// 隐藏快速问题
function hideQuickQuestions() {
    quickQuestions.style.display = 'none';
}

// 更新进度条
function updateProgress(progress) {
    const progressBar = document.getElementById('analysis-progress');
    progressBar.style.width = progress + '%';
    progressBar.setAttribute('aria-valuenow', progress);
}

// 显示分析报告
function showAnalysisReport(report) {
    const reportContent = document.getElementById('report-content');
    reportContent.innerHTML = formatReport(report.report_text);
    
    const reportModal = new bootstrap.Modal(document.getElementById('reportModal'));
    reportModal.show();
}

// 格式化报告内容
function formatReport(reportText) {
    // 简单的报告格式化
    return reportText
        .replace(/^(\d+\.\s.*?)$/gm, '<h4>$1</h4>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n\n/g, '</p><p>')
        .replace(/^/, '<p>')
        .replace(/$/, '</p>');
}

// 加载支持的文件格式
async function loadSupportedFormats() {
    try {
        const response = await fetch('/upload/supported-formats');
        const data = await response.json();

        const formatInfo = document.getElementById('format-info');
        const availableFormats = [];
        const unavailableFormats = [];

        for (const [mimeType, info] of Object.entries(data.supported_types)) {
            if (info.available) {
                availableFormats.push(info.name);
            } else {
                unavailableFormats.push(info.name);
            }
        }

        let infoText = `支持: ${availableFormats.join(', ')}`;
        if (unavailableFormats.length > 0) {
            infoText += ` | 需要依赖: ${unavailableFormats.join(', ')}`;
        }

        formatInfo.textContent = infoText;

    } catch (error) {
        console.error('加载支持格式失败:', error);
        document.getElementById('format-info').textContent = '格式信息加载失败';
    }
}

// 文件处理函数
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

// 上传文件
async function uploadFile(file) {
    const uploadProgress = document.getElementById('upload-progress');
    const uploadProgressBar = document.getElementById('upload-progress-bar');
    const uploadStatus = document.getElementById('upload-status');

    try {
        // 显示上传进度
        uploadProgress.style.display = 'block';
        uploadProgressBar.style.width = '0%';
        uploadStatus.textContent = '准备上传...';

        // 检查文件大小 (10MB限制)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            throw new Error('文件大小超过10MB限制');
        }

        const formData = new FormData();
        formData.append('file', file);

        // 模拟上传进度
        uploadProgressBar.style.width = '30%';
        uploadStatus.textContent = '上传中...';

        const response = await fetch('/upload/business-plan', {
            method: 'POST',
            body: formData
        });

        uploadProgressBar.style.width = '80%';
        uploadStatus.textContent = '解析中...';

        const result = await response.json();

        uploadProgressBar.style.width = '100%';

        if (result.success) {
            uploadStatus.textContent = '上传成功！';

            // 显示成功消息
            addMessage('assistant', `文件"${file.name}"上传并解析成功！让我为您展示分析结果...`);

            // 显示文件分析结果
            showFileAnalysisResult(result);

            // 隐藏进度条
            setTimeout(() => {
                uploadProgress.style.display = 'none';
            }, 2000);

        } else {
            throw new Error(result.error || '上传失败');
        }

    } catch (error) {
        console.error('文件上传失败:', error);
        uploadStatus.textContent = '上传失败';
        uploadProgressBar.classList.add('bg-danger');

        let errorMessage = '文件上传失败：' + error.message;
        if (error.message.includes('413')) {
            errorMessage = '文件太大，请选择小于10MB的文件';
        } else if (error.message.includes('400')) {
            errorMessage = '不支持的文件格式或文件解析失败';
        }

        addMessage('assistant', errorMessage);

        // 隐藏进度条
        setTimeout(() => {
            uploadProgress.style.display = 'none';
            uploadProgressBar.classList.remove('bg-danger');
        }, 3000);
    }
}

// 显示文件分析结果
function showFileAnalysisResult(result) {
    // 填充文件基本信息
    const fileBasicInfo = document.getElementById('file-basic-info');
    fileBasicInfo.innerHTML = `
        <p><strong>文件名:</strong> ${result.file_info.name}</p>
        <p><strong>文件大小:</strong> ${formatFileSize(result.file_info.size)}</p>
        <p><strong>文件类型:</strong> ${result.file_info.type}</p>
        <p><strong>解析时间:</strong> ${new Date(result.file_info.parsed_at).toLocaleString()}</p>
    `;

    // 填充元数据
    const fileMetadata = document.getElementById('file-metadata');
    let metadataHtml = '';
    for (const [key, value] of Object.entries(result.metadata)) {
        if (value !== null && value !== undefined && value !== '') {
            metadataHtml += `<p><strong>${formatMetadataKey(key)}:</strong> ${value}</p>`;
        }
    }
    fileMetadata.innerHTML = metadataHtml || '<p class="text-muted">无额外元数据</p>';

    // 填充分析摘要
    const analysisSummaryContent = document.getElementById('analysis-summary-content');
    analysisSummaryContent.innerHTML = result.analysis_summary.replace(/\n/g, '<br>');

    // 填充创业计划书分析（如果有）
    if (result.business_plan_sections && Object.keys(result.business_plan_sections).length > 0) {
        document.getElementById('business-plan-analysis').style.display = 'block';

        const planSections = document.getElementById('plan-sections');
        let sectionsHtml = '<h6>检测到的部分:</h6>';
        for (const [section, content] of Object.entries(result.business_plan_sections)) {
            const hasContent = content && content.trim().length > 0;
            const indicator = hasContent ? 'complete' : 'incomplete';
            sectionsHtml += `
                <p>
                    <span class="section-indicator ${indicator}"></span>
                    ${formatSectionName(section)}
                    ${hasContent ? `(${content.length} 字符)` : '(缺失)'}
                </p>
            `;
        }
        planSections.innerHTML = sectionsHtml;

        const validationResults = document.getElementById('validation-results');
        const validation = result.validation_result;
        validationResults.innerHTML = `
            <h6>完整性评估:</h6>
            <p><strong>完整性评分:</strong> ${validation.completeness_score.toFixed(1)}%</p>
            <p><strong>状态:</strong> ${validation.is_complete ? '✅ 完整' : '⚠️ 不完整'}</p>
            ${validation.suggestions.length > 0 ?
                '<p><strong>建议:</strong></p><ul>' +
                validation.suggestions.map(s => `<li>${s}</li>`).join('') +
                '</ul>' : ''
            }
        `;
    }

    // 填充内容预览
    const contentPreview = document.getElementById('content-preview-text');
    contentPreview.textContent = result.content_preview;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('fileAnalysisModal'));
    modal.show();
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化元数据键名
function formatMetadataKey(key) {
    const keyMap = {
        'format': '格式',
        'pages': '页数',
        'paragraphs': '段落数',
        'tables': '表格数',
        'slides': '幻灯片数',
        'sheets': '工作表',
        'encoding': '编码',
        'lines': '行数',
        'characters': '字符数',
        'title': '标题',
        'author': '作者',
        'subject': '主题',
        'created': '创建时间',
        'modified': '修改时间'
    };
    return keyMap[key] || key;
}

// 格式化部分名称
function formatSectionName(section) {
    const sectionMap = {
        'executive_summary': '执行摘要',
        'market_analysis': '市场分析',
        'business_model': '商业模式',
        'financial_projections': '财务预测',
        'team': '团队介绍',
        'risk_analysis': '风险分析',
        'implementation_plan': '实施计划'
    };
    return sectionMap[section] || section;
}

// 加载系统统计信息
async function loadSystemStats() {
    try {
        const response = await fetch('/system/stats');
        const stats = await response.json();
        
        document.getElementById('doc-count').textContent = 
            stats.vector_store_stats?.total_documents || '0';
        document.getElementById('system-status').textContent = 
            stats.is_initialized ? '就绪' : '初始化中';
    } catch (error) {
        console.error('加载系统统计失败:', error);
        document.getElementById('system-status').textContent = '错误';
    }
}
