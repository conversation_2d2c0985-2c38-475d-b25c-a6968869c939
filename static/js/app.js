// 全局变量
let currentIndustry = null;
let isTyping = false;
let conversationStarted = false;

// DOM元素
const industryList = document.getElementById('industry-list');
const chatMessages = document.getElementById('chat-messages');
const messageInput = document.getElementById('message-input');
const sendButton = document.getElementById('send-button');
const welcomeScreen = document.getElementById('welcome-screen');
const chatInput = document.getElementById('chat-input');
const expertInfo = document.getElementById('expert-info');
const progressContainer = document.getElementById('progress-container');
const quickQuestions = document.getElementById('quick-questions');
const questionButtons = document.getElementById('question-buttons');
const fileInput = document.getElementById('file-input');
const uploadArea = document.getElementById('upload-area');

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    loadIndustries();
    loadSystemStats();
    setupEventListeners();
});

// 加载行业列表
async function loadIndustries() {
    try {
        const response = await fetch('/industries');
        const data = await response.json();
        
        industryList.innerHTML = '';
        data.industries.forEach(industry => {
            const card = document.createElement('div');
            card.className = 'industry-card';
            card.dataset.industry = industry.id;
            card.innerHTML = `
                <i class="fas ${getIndustryIcon(industry.id)}"></i>
                <div class="industry-name">${industry.name}</div>
            `;
            card.addEventListener('click', () => selectIndustry(industry));
            industryList.appendChild(card);
        });
    } catch (error) {
        console.error('加载行业列表失败:', error);
    }
}

// 获取行业图标
function getIndustryIcon(industryId) {
    const icons = {
        'tech': 'fa-laptop-code',
        'finance': 'fa-coins',
        'healthcare': 'fa-heartbeat',
        'education': 'fa-graduation-cap',
        'retail': 'fa-shopping-cart',
        'manufacturing': 'fa-industry',
        'energy': 'fa-bolt',
        'agriculture': 'fa-seedling'
    };
    return icons[industryId] || 'fa-briefcase';
}

// 选择行业
async function selectIndustry(industry) {
    // 更新UI
    document.querySelectorAll('.industry-card').forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelector(`[data-industry="${industry.id}"]`).classList.add('selected');
    
    currentIndustry = industry.id;
    
    // 开始对话
    try {
        showTyping();
        const formData = new FormData();
        formData.append('industry', industry.id);
        
        const response = await fetch('/chat/start', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        hideTyping();
        
        if (result.success) {
            // 隐藏欢迎屏幕，显示聊天界面
            welcomeScreen.style.display = 'none';
            chatInput.style.display = 'block';
            expertInfo.style.display = 'flex';
            progressContainer.style.display = 'block';
            
            // 更新专家信息
            document.getElementById('expert-name').textContent = result.expert_name;
            document.getElementById('expert-description').textContent = result.expert_personality;
            
            // 显示欢迎消息
            addMessage('assistant', result.welcome_message);
            
            // 显示初始问题
            if (result.initial_questions && result.initial_questions.length > 0) {
                showQuickQuestions(result.initial_questions);
            }
            
            // 启用输入
            messageInput.disabled = false;
            sendButton.disabled = false;
            messageInput.focus();
            
            conversationStarted = true;
            updateProgress(result.progress || 0);
            
        } else {
            alert('启动对话失败: ' + result.error);
        }
    } catch (error) {
        hideTyping();
        console.error('选择行业失败:', error);
        alert('选择行业失败，请重试');
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 发送消息
    sendButton.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    // 文件上传
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('drop', handleFileDrop);
    fileInput.addEventListener('change', handleFileSelect);
}

// 发送消息
async function sendMessage() {
    const message = messageInput.value.trim();
    if (!message || isTyping) return;
    
    // 添加用户消息
    addMessage('user', message);
    messageInput.value = '';
    hideQuickQuestions();
    
    try {
        showTyping();
        const formData = new FormData();
        formData.append('message', message);
        formData.append('message_type', 'text');
        
        const response = await fetch('/chat/message', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        hideTyping();
        
        if (result.success) {
            // 添加助手回复
            addMessage('assistant', result.message);
            
            // 更新进度
            if (result.progress) {
                updateProgress(result.progress);
            }
            
            // 显示快速问题
            if (result.next_questions && result.next_questions.length > 0) {
                showQuickQuestions(result.next_questions);
            }
            
            // 如果有分析报告，显示报告
            if (result.analysis_report) {
                showAnalysisReport(result.analysis_report);
            }
            
        } else {
            addMessage('assistant', '抱歉，处理您的消息时出现了问题。请稍后再试。');
        }
    } catch (error) {
        hideTyping();
        console.error('发送消息失败:', error);
        addMessage('assistant', '网络连接出现问题，请检查网络后重试。');
    }
}

// 添加消息到聊天区域
function addMessage(role, content) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}`;
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = formatMessage(content);
    
    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = new Date().toLocaleTimeString();
    
    contentDiv.appendChild(timeDiv);
    messageDiv.appendChild(contentDiv);
    chatMessages.appendChild(messageDiv);
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 格式化消息内容
function formatMessage(content) {
    // 简单的Markdown支持
    return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>');
}

// 显示打字指示器
function showTyping() {
    if (isTyping) return;
    
    isTyping = true;
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message assistant';
    typingDiv.id = 'typing-indicator';
    
    typingDiv.innerHTML = `
        <div class="typing-indicator">
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
    `;
    
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 隐藏打字指示器
function hideTyping() {
    isTyping = false;
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// 显示快速问题
function showQuickQuestions(questions) {
    questionButtons.innerHTML = '';
    
    questions.forEach(question => {
        const button = document.createElement('span');
        button.className = 'question-btn';
        button.textContent = question;
        button.addEventListener('click', () => {
            messageInput.value = question;
            sendMessage();
        });
        questionButtons.appendChild(button);
    });
    
    quickQuestions.style.display = 'block';
}

// 隐藏快速问题
function hideQuickQuestions() {
    quickQuestions.style.display = 'none';
}

// 更新进度条
function updateProgress(progress) {
    const progressBar = document.getElementById('analysis-progress');
    progressBar.style.width = progress + '%';
    progressBar.setAttribute('aria-valuenow', progress);
}

// 显示分析报告
function showAnalysisReport(report) {
    const reportContent = document.getElementById('report-content');
    reportContent.innerHTML = formatReport(report.report_text);
    
    const reportModal = new bootstrap.Modal(document.getElementById('reportModal'));
    reportModal.show();
}

// 格式化报告内容
function formatReport(reportText) {
    // 简单的报告格式化
    return reportText
        .replace(/^(\d+\.\s.*?)$/gm, '<h4>$1</h4>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n\n/g, '</p><p>')
        .replace(/^/, '<p>')
        .replace(/$/, '</p>');
}

// 文件处理函数
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.style.background = 'rgba(255,255,255,0.2)';
}

function handleFileDrop(e) {
    e.preventDefault();
    uploadArea.style.background = '';
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

// 上传文件
async function uploadFile(file) {
    try {
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await fetch('/upload/business-plan', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            addMessage('assistant', `文件"${file.name}"上传成功！我已经收到您的创业计划书，让我来分析一下...`);
            // 可以在这里触发对文件内容的分析
        } else {
            addMessage('assistant', '文件上传失败，请重试。');
        }
    } catch (error) {
        console.error('文件上传失败:', error);
        addMessage('assistant', '文件上传出现问题，请检查网络连接。');
    }
}

// 加载系统统计信息
async function loadSystemStats() {
    try {
        const response = await fetch('/system/stats');
        const stats = await response.json();
        
        document.getElementById('doc-count').textContent = 
            stats.vector_store_stats?.total_documents || '0';
        document.getElementById('system-status').textContent = 
            stats.is_initialized ? '就绪' : '初始化中';
    } catch (error) {
        console.error('加载系统统计失败:', error);
        document.getElementById('system-status').textContent = '错误';
    }
}
