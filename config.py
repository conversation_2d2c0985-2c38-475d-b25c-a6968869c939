"""
配置文件
"""
import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # 通义千问API配置
    QWEN_API_KEY = os.getenv("QWEN_API_KEY", "sk-5f79784609a740f3a6cf68ebb08b4c14")
    QWEN_BASE_URL = os.getenv("QWEN_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
    
    # 数据库配置
    DB_SERVER = os.getenv("DB_SERVER", "localhost")
    DB_DATABASE = os.getenv("DB_DATABASE", "HurunDB")
    DB_USERNAME = os.getenv("DB_USERNAME", "")
    DB_PASSWORD = os.getenv("DB_PASSWORD", "")
    DB_DRIVER = os.getenv("DB_DRIVER", "ODBC Driver 17 for SQL Server")
    
    # 向量数据库配置
    VECTOR_DB_PATH = os.getenv("VECTOR_DB_PATH", "./vector_db")
    CHROMA_DB_PATH = os.getenv("CHROMA_DB_PATH", "./chroma_db")
    
    # 应用配置
    APP_HOST = os.getenv("APP_HOST", "0.0.0.0")
    APP_PORT = int(os.getenv("APP_PORT", "8000"))
    DEBUG = os.getenv("DEBUG", "True").lower() == "true"
    
    # RAG配置
    PARENT_CHUNK_SIZE = int(os.getenv("PARENT_CHUNK_SIZE", "1024"))
    CHILD_CHUNK_SIZE = int(os.getenv("CHILD_CHUNK_SIZE", "512"))
    CHUNK_OVERLAP = int(os.getenv("CHUNK_OVERLAP", "50"))
    TOP_K_RETRIEVAL = int(os.getenv("TOP_K_RETRIEVAL", "10"))
    RERANK_TOP_K = int(os.getenv("RERANK_TOP_K", "5"))
    
    # 模型配置
    EMBEDDING_MODEL = "text-embedding-v4"
    RERANK_MODEL = "gte-rerank"
    CHAT_MODEL = "qwen-plus"
    
    @property
    def database_url(self):
        return f"mssql+pyodbc://{self.DB_USERNAME}:{self.DB_PASSWORD}@{self.DB_SERVER}/{self.DB_DATABASE}?driver={self.DB_DRIVER.replace(' ', '+')}"

config = Config()
