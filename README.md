# 创业分析智能体

基于RAG技术的U30创业者智能分析助手，为年轻创业者提供专业的创业分析和建议。

## 🚀 功能特点

### 核心功能
- **双模式分析系统**:
  - 📄 **文档分析模式**: 上传商业计划书，直接生成专业分析报告
  - 🤖 **引导式分析模式**: 通过专业问答，深入了解项目并生成报告
- **多行业专家角色**: 支持8个主要行业的专业分析
- **多格式文档上传**: 支持PDF、Word、Markdown、Excel、PowerPoint等12种文件格式
- **智能文档解析**: 自动提取创业计划书结构，评估完整性
- **个性化分析报告**: 基于不同输入方式生成详细的创业分析和建议
- **实时进度跟踪**: 可视化分析进度

### 技术特色
- **RAG技术**: 基于检索增强生成的知识库系统
- **父子分段**: 智能文档分段策略，提高检索精度
- **混合检索**: 结合ChromaDB和FAISS的向量检索
- **通义千问集成**: 使用阿里云通义千问的embedding和对话模型

## 🏗️ 系统架构

```
创业分析智能体
├── 前端界面 (HTML/CSS/JavaScript)
├── FastAPI后端
├── RAG系统
│   ├── 文档分段器 (父子分段)
│   ├── 向量存储 (ChromaDB + FAISS)
│   └── 通义千问API客户端
├── 智能体系统
│   ├── 行业专家角色
│   └── 对话管理
└── 数据库连接 (SQL Server)
```

## 🎯 支持的行业

1. **科技互联网** - 人工智能、软件开发、互联网平台
2. **金融科技** - 数字支付、区块链、投资理财
3. **医疗健康** - 生物医药、医疗器械、健康管理
4. **教育培训** - 在线教育、职业培训、知识付费
5. **新零售** - 电商平台、社交电商、智能零售
6. **智能制造** - 工业4.0、智能工厂、自动化设备
7. **新能源** - 太阳能、风能、储能技术
8. **智慧农业** - 农业科技、精准农业、农产品电商

## 📄 支持的文件格式

### 完全支持的格式
- **PDF文档** (.pdf) - 便携式文档格式
- **Word文档** (.docx) - Microsoft Word文档
- **Markdown文档** (.md, .markdown) - 标记语言文档
- **Excel表格** (.xlsx) - Microsoft Excel表格
- **PowerPoint演示文稿** (.pptx) - Microsoft PowerPoint演示文稿
- **文本文件** (.txt) - 纯文本文件
- **JSON文件** (.json) - 结构化数据文件
- **CSV文件** (.csv) - 逗号分隔值文件
- **HTML文件** (.html, .htm) - 网页文件

### 需要格式转换的文件
- **旧版Word文档** (.doc) → 请转换为 .docx
- **旧版Excel表格** (.xls) → 请转换为 .xlsx
- **旧版PowerPoint** (.ppt) → 请转换为 .pptx

### 文档解析特性
- **智能结构识别**: 自动识别创业计划书的各个部分
- **完整性评估**: 评估文档完整性并提供改进建议
- **元数据提取**: 提取文档属性、页数、字数等信息
- **内容预览**: 提供文档内容的快速预览

## 🛠️ 安装和运行

### 环境要求
- Python 3.8+
- SQL Server数据库 (可选，用于完整版本)

### 快速开始 (演示版本)

1. **克隆项目**
```bash
git clone <repository-url>
cd startup-analysis-agent
```

2. **安装依赖**
```bash
# 基础依赖
pip install fastapi uvicorn python-multipart jinja2 python-dotenv httpx pydantic

# 文档解析依赖
pip install PyPDF2 python-docx markdown openpyxl python-pptx aiofiles
```

3. **启动应用**
```bash
python main.py
```

4. **访问应用**
打开浏览器访问: http://localhost:8000

5. **测试功能演示**
```bash
# 测试文件上传功能
python demo_file_upload.py

# 测试双模式分析功能
python demo_dual_analysis.py
```

### 完整版本部署

1. **安装完整依赖**
```bash
pip install -r requirements.txt
```

2. **配置环境变量**
复制 `.env.example` 为 `.env` 并配置:
```bash
# 通义千问API配置
QWEN_API_KEY=your_api_key_here

# 数据库配置
DB_SERVER=your_server
DB_DATABASE=HurunDB
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

3. **初始化数据库**
确保SQL Server数据库HurunDB已创建并包含相关数据

4. **启动应用**
```bash
python main.py
```

## 📱 使用指南

### 🚀 双模式分析系统

#### 方式一：文档分析模式 📄
**适用场景**: 已有完整商业计划书，需要快速专业分析

1. **选择分析方式**: 点击"文档分析"模式
2. **选择行业专家**: 从8个专业领域中选择最适合的专家
3. **上传商业计划书**: 支持12种文件格式（PDF、Word、Markdown等）
4. **选择分析类型**:
   - **快速分析**: 仅解析文档结构和内容
   - **完整分析**: 生成详细的专业分析报告
5. **获取分析报告**: 立即获得基于文档的专业分析

#### 方式二：引导式分析模式 🤖
**适用场景**: 项目初期阶段，需要专业指导和深入分析

1. **选择分析方式**: 点击"引导分析"模式
2. **选择行业专家**: 从8个专业领域中选择最适合的专家
3. **回答专业问题**: 通过多个阶段的专业问题深入了解项目
   - **基本信息阶段**: 项目名称、目标客户、核心问题
   - **市场分析阶段**: 市场规模、竞争对手、进入策略
   - **商业模式阶段**: 盈利模式、成本结构、盈亏平衡
   - **团队与资金阶段**: 团队背景、资金需求、发展里程碑
   - **行业特定问题**: 技术细节、合规要求、临床验证等
4. **实时进度跟踪**: 查看分析进度和当前阶段
5. **生成分析报告**: 基于详细问答生成个性化分析报告

#### 两种模式对比
| 特性 | 文档分析模式 | 引导式分析模式 |
|------|------------|--------------|
| 输入方式 | 上传文档文件 | 回答专业问题 |
| 分析速度 | 快速（秒级） | 较慢（需多轮对话） |
| 分析深度 | 基于现有文档内容 | 深入挖掘项目细节 |
| 个性化程度 | 中等 | 高（针对性问题） |
| 适用场景 | 已有完整商业计划书 | 项目初期或需要指导 |
| 专家互动 | 无 | 有（多轮对话） |
| 报告质量 | 依赖文档质量 | 基于详细问答 |
| 用户体验 | 简单快捷 | 引导式、教育性强 |

## 🎨 界面特色

- **响应式设计**: 支持桌面和移动设备
- **实时对话**: 流畅的聊天体验
- **进度可视化**: 清晰的分析进度显示
- **专家角色**: 每个行业都有独特的专家形象
- **报告展示**: 专业的分析报告格式

## 🔧 技术栈

### 后端
- **FastAPI**: 现代Python Web框架
- **Uvicorn**: ASGI服务器
- **SQLAlchemy**: 数据库ORM
- **ChromaDB**: 向量数据库
- **FAISS**: 向量相似度搜索

### 前端
- **HTML5/CSS3**: 现代Web标准
- **Bootstrap 5**: 响应式UI框架
- **JavaScript**: 原生JS实现交互
- **Font Awesome**: 图标库

### AI/ML
- **通义千问**: 阿里云大语言模型
- **text-embedding-v4**: 文本嵌入模型
- **RAG**: 检索增强生成技术

## 📊 系统特点

### RAG技术优势
- **知识库驱动**: 基于真实行业数据的专业分析
- **上下文感知**: 结合对话历史和知识库内容
- **实时更新**: 支持知识库的动态更新

### 智能体特色
- **角色扮演**: 8个专业行业专家角色
- **个性化交流**: 亲切幽默的沟通风格
- **专业深度**: 针对不同行业的专业问题

### 用户体验
- **渐进式引导**: 从基础信息到深度分析
- **可视化反馈**: 实时进度和状态显示
- **多模态输入**: 支持文本和文档输入

## 🚧 开发计划

### 已完成
- [x] 基础架构搭建
- [x] 行业专家系统
- [x] 对话管理
- [x] Web界面
- [x] 演示版本

### 进行中
- [ ] RAG系统完整实现
- [ ] 数据库集成
- [ ] 文档解析功能

### 计划中
- [ ] 用户账户系统
- [ ] 历史记录管理
- [ ] 报告导出功能
- [ ] 移动端适配
- [ ] 多语言支持

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 邮箱: [<EMAIL>]

---

**让创业更智能，让梦想更精准！** 🌟
