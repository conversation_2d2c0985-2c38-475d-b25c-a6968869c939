"""
创业分析智能体主程序 - 简化演示版本
"""
from fastapi import FastAPI, Form, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request
from fastapi.responses import HTMLResponse, JSONResponse
import uvicorn
import os
import json
from datetime import datetime

# 创建FastAPI应用
app = FastAPI(title="创业分析智能体", description="基于RAG技术的U30创业者智能分析助手")

# 静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 模拟数据存储
conversation_data = {}
current_expert = None

# 模拟行业专家数据
INDUSTRY_EXPERTS = {
    "tech": {
        "name": "小智",
        "personality": "我是小智，一个对前沿科技充满热情的技术专家！我喜欢用数据说话，也爱分享最新的技术趋势。让我们一起探索科技创业的无限可能吧！🚀",
        "questions": [
            "请简要描述您的创业项目或想法",
            "您的技术方案的核心创新点是什么？",
            "您的目标客户群体是谁？",
            "您认为这个项目解决了什么核心问题？"
        ]
    },
    "finance": {
        "name": "小金",
        "personality": "嗨！我是小金，金融科技领域的资深分析师。我擅长从风险和收益的角度分析项目，也很关注合规和可持续发展。让我帮你打造稳健的商业模式！💰",
        "questions": [
            "请简要描述您的金融科技项目",
            "您了解相关的金融监管要求吗？",
            "您的盈利模式是什么？",
            "如何确保业务合规性？"
        ]
    },
    "healthcare": {
        "name": "小医",
        "personality": "你好！我是小医，专注医疗健康领域的创新专家。我深知这个行业的特殊性和社会责任，让我们一起为人类健康事业贡献力量！⚕️",
        "questions": [
            "请描述您的医疗健康项目",
            "产品是否需要临床试验？",
            "如何证明产品的临床价值？",
            "与医院或医生的合作模式是什么？"
        ]
    }
}

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """主页"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/industries")
async def get_industries():
    """获取支持的行业列表"""
    industries = [
        {"id": "tech", "name": "科技互联网", "description": "人工智能、软件开发、互联网平台等"},
        {"id": "finance", "name": "金融科技", "description": "数字支付、区块链、投资理财等"},
        {"id": "healthcare", "name": "医疗健康", "description": "生物医药、医疗器械、健康管理等"},
        {"id": "education", "name": "教育培训", "description": "在线教育、职业培训、知识付费等"},
        {"id": "retail", "name": "新零售", "description": "电商平台、社交电商、智能零售等"},
        {"id": "manufacturing", "name": "智能制造", "description": "工业4.0、智能工厂、自动化设备等"},
        {"id": "energy", "name": "新能源", "description": "太阳能、风能、储能技术等"},
        {"id": "agriculture", "name": "智慧农业", "description": "农业科技、精准农业、农产品电商等"}
    ]
    return {"industries": industries}

@app.post("/chat/start")
async def start_chat(industry: str = Form(...)):
    """开始对话"""
    global current_expert, conversation_data

    try:
        # 获取行业专家信息
        expert = INDUSTRY_EXPERTS.get(industry, INDUSTRY_EXPERTS["tech"])
        current_expert = expert

        # 初始化对话数据
        conversation_data = {
            "industry": industry,
            "expert": expert,
            "messages": [],
            "state": "initial",
            "progress": 0
        }

        welcome_message = f"你好！{expert['personality']}\n\n我很高兴能为您提供创业分析服务。让我们开始吧！"

        return {
            "success": True,
            "expert_name": expert["name"],
            "expert_personality": expert["personality"],
            "welcome_message": welcome_message,
            "initial_questions": expert["questions"][:2],
            "analysis_state": "initial"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat/message")
async def send_message(message: str = Form(...), message_type: str = Form("text")):
    """发送消息"""
    global conversation_data, current_expert

    try:
        if not current_expert:
            raise HTTPException(status_code=400, detail="请先选择行业")

        # 记录用户消息
        conversation_data["messages"].append({
            "role": "user",
            "content": message,
            "timestamp": datetime.now().isoformat()
        })

        # 生成回复（简化版本）
        response_message = generate_mock_response(message, conversation_data)

        # 记录助手回复
        conversation_data["messages"].append({
            "role": "assistant",
            "content": response_message,
            "timestamp": datetime.now().isoformat()
        })

        # 更新状态和进度
        message_count = len([m for m in conversation_data["messages"] if m["role"] == "user"])

        if message_count >= 4:
            conversation_data["state"] = "analyzing"
            conversation_data["progress"] = 75

            # 生成分析报告
            analysis_report = generate_mock_analysis_report(conversation_data)

            return {
                "success": True,
                "message": response_message,
                "analysis_state": "analyzing",
                "progress": 75,
                "analysis_report": analysis_report
            }
        else:
            conversation_data["state"] = "questioning"
            conversation_data["progress"] = message_count * 20

            return {
                "success": True,
                "message": response_message,
                "analysis_state": "questioning",
                "progress": conversation_data["progress"],
                "next_questions": current_expert["questions"][message_count:message_count+2]
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/system/stats")
async def get_system_stats():
    """获取系统统计信息"""
    return {
        "is_initialized": True,
        "vector_store_stats": {"total_documents": 1000},
        "knowledge_base_info": {"status": "ready"}
    }

def generate_mock_response(user_message: str, conversation_data: dict) -> str:
    """生成模拟回复"""
    industry = conversation_data["industry"]
    expert = conversation_data["expert"]
    message_count = len([m for m in conversation_data["messages"] if m["role"] == "user"])

    # 根据行业和消息数量生成不同的回复
    if industry == "tech":
        responses = [
            f"很棒的想法！从技术角度来看，{user_message[:50]}... 这个方向确实有很大潜力。让我问您几个关键问题来更好地了解您的项目。",
            f"我注意到您提到了{user_message[:30]}... 这在技术实现上需要考虑可扩展性和安全性。您的团队在这方面有什么经验吗？",
            f"基于您的描述，我看到了这个项目的技术优势。现在让我们深入分析一下市场机会和商业模式。",
            "太好了！我已经收集了足够的信息。让我为您生成一份详细的技术创业分析报告..."
        ]
    elif industry == "finance":
        responses = [
            f"从金融科技的角度分析，{user_message[:50]}... 这个领域确实需要创新解决方案。合规性将是关键考虑因素。",
            f"您提到的{user_message[:30]}... 在金融领域需要特别注意风险控制。您对相关监管政策了解吗？",
            f"很好！基于您的回答，我能看出您对金融市场有深入理解。让我们继续探讨盈利模式。",
            "完美！现在我有足够信息为您生成金融科技项目的专业分析报告..."
        ]
    else:
        responses = [
            f"非常有趣的项目！{user_message[:50]}... 让我从专业角度为您分析一下。",
            f"您的想法很有创意。关于{user_message[:30]}... 我想了解更多细节。",
            f"基于我们的对话，我对您的项目有了更清晰的认识。让我们深入分析。",
            "太棒了！让我为您生成一份详细的行业分析报告..."
        ]

    return responses[min(message_count - 1, len(responses) - 1)]

def generate_mock_analysis_report(conversation_data: dict) -> dict:
    """生成模拟分析报告"""
    industry = conversation_data["industry"]
    expert = conversation_data["expert"]

    report_text = f"""
# 创业项目分析报告

## 1. 项目概述
基于我们的深入交流，您的{industry}领域创业项目展现出了很大的潜力。项目具有明确的市场定位和创新价值。

## 2. 市场分析
**市场机会**: {industry}行业正处于快速发展期，市场需求旺盛。
**竞争格局**: 虽然竞争激烈，但仍有细分市场机会。
**目标客户**: 您的目标客户群体定位清晰，具有较强的付费意愿。

## 3. 商业模式评估
**盈利模式**: 建议采用多元化收入结构，降低单一收入来源风险。
**成本结构**: 需要重点关注获客成本和运营成本的控制。
**扩展性**: 项目具备良好的可扩展性，适合规模化发展。

## 4. 风险分析
**主要风险**:
- 市场竞争加剧
- 技术迭代风险
- 资金链风险
- 团队稳定性风险

**应对策略**:
- 建立技术壁垒
- 多元化融资渠道
- 完善团队激励机制

## 5. 发展建议
1. **短期目标**: 专注产品打磨，获得种子用户验证
2. **中期目标**: 扩大市场份额，建立品牌影响力
3. **长期目标**: 成为行业领导者，考虑国际化发展

## 6. 行业预测
{industry}行业在未来3-5年将继续保持高速增长，预计年增长率达到20-30%。新技术的应用将带来更多创新机会。

## 7. 下一步行动计划
1. 完善商业计划书
2. 寻找合适的投资人
3. 组建核心团队
4. 开发MVP产品
5. 进行市场测试

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*分析专家: {expert['name']}*
"""

    return {
        "report_text": report_text,
        "generated_at": datetime.now().isoformat(),
        "expert": expert["name"],
        "industry": industry
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
