"""
创业分析智能体主程序 - 增强版本
"""
from fastapi import FastAPI, Form, HTTPException, File, UploadFile
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request
from fastapi.responses import HTMLResponse, JSONResponse
import uvicorn
import os
import json
import aiofiles
from datetime import datetime
from typing import List

from src.utils.document_parser import EnhancedDocumentParser

# 创建FastAPI应用
app = FastAPI(title="创业分析智能体", description="基于RAG技术的U30创业者智能分析助手")

# 静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 初始化文档解析器
document_parser = EnhancedDocumentParser()

# 模拟数据存储
conversation_data = {}
current_expert = None

# 确保上传目录存在
os.makedirs("uploads", exist_ok=True)

# 模拟行业专家数据
INDUSTRY_EXPERTS = {
    "tech": {
        "name": "小智",
        "personality": "我是小智，一个对前沿科技充满热情的技术专家！我喜欢用数据说话，也爱分享最新的技术趋势。让我们一起探索科技创业的无限可能吧！🚀",
        "questions": [
            "请简要描述您的创业项目或想法",
            "您的技术方案的核心创新点是什么？",
            "您的目标客户群体是谁？",
            "您认为这个项目解决了什么核心问题？"
        ]
    },
    "finance": {
        "name": "小金",
        "personality": "嗨！我是小金，金融科技领域的资深分析师。我擅长从风险和收益的角度分析项目，也很关注合规和可持续发展。让我帮你打造稳健的商业模式！💰",
        "questions": [
            "请简要描述您的金融科技项目",
            "您了解相关的金融监管要求吗？",
            "您的盈利模式是什么？",
            "如何确保业务合规性？"
        ]
    },
    "healthcare": {
        "name": "小医",
        "personality": "你好！我是小医，专注医疗健康领域的创新专家。我深知这个行业的特殊性和社会责任，让我们一起为人类健康事业贡献力量！⚕️",
        "questions": [
            "请描述您的医疗健康项目",
            "产品是否需要临床试验？",
            "如何证明产品的临床价值？",
            "与医院或医生的合作模式是什么？"
        ]
    }
}

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """主页"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/industries")
async def get_industries():
    """获取支持的行业列表"""
    industries = [
        {"id": "tech", "name": "科技互联网", "description": "人工智能、软件开发、互联网平台等"},
        {"id": "finance", "name": "金融科技", "description": "数字支付、区块链、投资理财等"},
        {"id": "healthcare", "name": "医疗健康", "description": "生物医药、医疗器械、健康管理等"},
        {"id": "education", "name": "教育培训", "description": "在线教育、职业培训、知识付费等"},
        {"id": "retail", "name": "新零售", "description": "电商平台、社交电商、智能零售等"},
        {"id": "manufacturing", "name": "智能制造", "description": "工业4.0、智能工厂、自动化设备等"},
        {"id": "energy", "name": "新能源", "description": "太阳能、风能、储能技术等"},
        {"id": "agriculture", "name": "智慧农业", "description": "农业科技、精准农业、农产品电商等"}
    ]
    return {"industries": industries}

@app.post("/chat/start")
async def start_chat(industry: str = Form(...)):
    """开始对话"""
    global current_expert, conversation_data

    try:
        # 获取行业专家信息
        expert = INDUSTRY_EXPERTS.get(industry, INDUSTRY_EXPERTS["tech"])
        current_expert = expert

        # 初始化对话数据
        conversation_data = {
            "industry": industry,
            "expert": expert,
            "messages": [],
            "state": "initial",
            "progress": 0
        }

        welcome_message = f"你好！{expert['personality']}\n\n我很高兴能为您提供创业分析服务。让我们开始吧！"

        return {
            "success": True,
            "expert_name": expert["name"],
            "expert_personality": expert["personality"],
            "welcome_message": welcome_message,
            "initial_questions": expert["questions"][:2],
            "analysis_state": "initial"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat/message")
async def send_message(message: str = Form(...), message_type: str = Form("text")):
    """发送消息"""
    global conversation_data, current_expert

    try:
        if not current_expert:
            raise HTTPException(status_code=400, detail="请先选择行业")

        # 记录用户消息
        conversation_data["messages"].append({
            "role": "user",
            "content": message,
            "timestamp": datetime.now().isoformat()
        })

        # 生成回复（简化版本）
        response_message = generate_mock_response(message, conversation_data)

        # 记录助手回复
        conversation_data["messages"].append({
            "role": "assistant",
            "content": response_message,
            "timestamp": datetime.now().isoformat()
        })

        # 更新状态和进度
        message_count = len([m for m in conversation_data["messages"] if m["role"] == "user"])

        if message_count >= 4:
            conversation_data["state"] = "analyzing"
            conversation_data["progress"] = 75

            # 生成分析报告
            analysis_report = generate_mock_analysis_report(conversation_data)

            return {
                "success": True,
                "message": response_message,
                "analysis_state": "analyzing",
                "progress": 75,
                "analysis_report": analysis_report
            }
        else:
            conversation_data["state"] = "questioning"
            conversation_data["progress"] = message_count * 20

            return {
                "success": True,
                "message": response_message,
                "analysis_state": "questioning",
                "progress": conversation_data["progress"],
                "next_questions": current_expert["questions"][message_count:message_count+2]
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/upload/business-plan")
async def upload_business_plan(file: UploadFile = File(...)):
    """上传创业计划书"""
    try:
        # 检查文件大小 (限制为10MB)
        max_size = 10 * 1024 * 1024  # 10MB
        file_size = 0

        # 生成唯一文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{file.filename}"
        file_path = os.path.join("uploads", filename)

        # 保存文件
        async with aiofiles.open(file_path, 'wb') as f:
            while chunk := await file.read(1024):
                file_size += len(chunk)
                if file_size > max_size:
                    os.remove(file_path)
                    raise HTTPException(status_code=413, detail="文件大小超过10MB限制")
                await f.write(chunk)

        # 获取文件类型信息
        file_type_info = document_parser.get_file_type_info(file_path)

        if not file_type_info["is_supported"]:
            os.remove(file_path)
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型: {file_type_info['detected_type']}"
            )

        if not file_type_info["parser_available"]:
            os.remove(file_path)
            raise HTTPException(
                status_code=400,
                detail=f"缺少解析此文件类型所需的依赖库: {file_type_info['format_info'].get('name', '未知格式')}"
            )

        # 解析文件内容
        parse_result = document_parser.parse_file(file_path)

        if not parse_result["success"]:
            os.remove(file_path)
            raise HTTPException(status_code=400, detail=f"文件解析失败: {parse_result['error']}")

        # 分析创业计划书结构
        if parse_result["content"]:
            business_plan_sections = document_parser.extract_business_plan_sections(parse_result["content"])
            validation_result = document_parser.validate_business_plan(business_plan_sections)
        else:
            business_plan_sections = {}
            validation_result = {"is_complete": False, "completeness_score": 0}

        # 生成分析摘要
        analysis_summary = generate_document_analysis(parse_result, business_plan_sections, validation_result)

        return {
            "success": True,
            "message": f"文件 '{file.filename}' 上传并解析成功！",
            "file_info": parse_result["file_info"],
            "content_preview": parse_result["content"][:500] + "..." if len(parse_result["content"]) > 500 else parse_result["content"],
            "metadata": parse_result["metadata"],
            "business_plan_sections": business_plan_sections,
            "validation_result": validation_result,
            "analysis_summary": analysis_summary,
            "full_content": parse_result["content"]  # 添加完整内容用于报告生成
        }

    except HTTPException:
        raise
    except Exception as e:
        # 清理文件
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@app.post("/upload/analyze-document")
async def analyze_document_directly(file: UploadFile = File(...)):
    """直接分析上传的商业计划书并生成报告"""
    try:
        # 先上传和解析文件
        upload_result = await upload_business_plan(file)

        if not upload_result["success"]:
            return upload_result

        # 基于文档内容生成分析报告
        report = await generate_document_based_report(upload_result)

        return {
            "success": True,
            "message": "文档分析完成！",
            "file_info": upload_result["file_info"],
            "analysis_report": report,
            "document_analysis": {
                "business_plan_sections": upload_result["business_plan_sections"],
                "validation_result": upload_result["validation_result"],
                "analysis_summary": upload_result["analysis_summary"]
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文档分析失败: {str(e)}")

@app.get("/upload/supported-formats")
async def get_supported_formats():
    """获取支持的文件格式"""
    return document_parser.get_supported_formats()

@app.post("/guided-analysis/start")
async def start_guided_analysis(industry: str = Form(...)):
    """开始引导式分析"""
    global conversation_data, current_expert

    try:
        # 获取行业专家信息
        expert = INDUSTRY_EXPERTS.get(industry, INDUSTRY_EXPERTS["tech"])
        current_expert = expert

        # 初始化引导式分析数据
        conversation_data = {
            "industry": industry,
            "expert": expert,
            "mode": "guided_analysis",  # 标记为引导式分析模式
            "responses": [],
            "current_stage": "basic_info",
            "progress": 0
        }

        # 获取引导问题
        guided_questions = get_guided_analysis_questions(industry)

        welcome_message = f"你好！我是{expert['name']}，{expert['personality']}\n\n我将通过一系列专业问题来深入了解您的创业项目，然后为您生成详细的分析报告。让我们开始吧！"

        return {
            "success": True,
            "expert_name": expert["name"],
            "expert_personality": expert["personality"],
            "welcome_message": welcome_message,
            "current_stage": "basic_info",
            "questions": guided_questions["basic_info"],
            "total_stages": len(guided_questions),
            "progress": 0
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/guided-analysis/answer")
async def submit_guided_answer(answer: str = Form(...), question_id: str = Form(...)):
    """提交引导式分析的答案"""
    global conversation_data, current_expert

    try:
        if not current_expert or conversation_data.get("mode") != "guided_analysis":
            raise HTTPException(status_code=400, detail="请先开始引导式分析")

        # 记录答案
        conversation_data["responses"].append({
            "question_id": question_id,
            "answer": answer,
            "timestamp": datetime.now().isoformat(),
            "stage": conversation_data["current_stage"]
        })

        # 获取引导问题
        guided_questions = get_guided_analysis_questions(conversation_data["industry"])
        stages = list(guided_questions.keys())
        current_stage_index = stages.index(conversation_data["current_stage"])

        # 检查当前阶段是否完成
        current_stage_responses = [r for r in conversation_data["responses"] if r["stage"] == conversation_data["current_stage"]]
        current_stage_questions = guided_questions[conversation_data["current_stage"]]

        if len(current_stage_responses) >= len(current_stage_questions):
            # 当前阶段完成，进入下一阶段
            if current_stage_index < len(stages) - 1:
                conversation_data["current_stage"] = stages[current_stage_index + 1]
                conversation_data["progress"] = int((current_stage_index + 1) / len(stages) * 80)  # 80%用于问答，20%用于生成报告

                return {
                    "success": True,
                    "message": f"很好！让我们继续下一个部分的问题。",
                    "current_stage": conversation_data["current_stage"],
                    "questions": guided_questions[conversation_data["current_stage"]],
                    "progress": conversation_data["progress"],
                    "stage_completed": True
                }
            else:
                # 所有阶段完成，生成报告
                conversation_data["progress"] = 90
                report = await generate_guided_analysis_report(conversation_data)
                conversation_data["progress"] = 100

                return {
                    "success": True,
                    "message": "太棒了！我已经收集了所有必要信息，为您生成了详细的分析报告。",
                    "analysis_completed": True,
                    "analysis_report": report,
                    "progress": 100
                }
        else:
            # 当前阶段未完成，继续当前阶段
            remaining_questions = current_stage_questions[len(current_stage_responses):]

            return {
                "success": True,
                "message": generate_follow_up_message(answer, conversation_data),
                "current_stage": conversation_data["current_stage"],
                "questions": remaining_questions,
                "progress": conversation_data["progress"],
                "stage_completed": False
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/system/stats")
async def get_system_stats():
    """获取系统统计信息"""
    # 统计上传文件数量
    upload_count = len([f for f in os.listdir("uploads") if os.path.isfile(os.path.join("uploads", f))]) if os.path.exists("uploads") else 0

    return {
        "is_initialized": True,
        "vector_store_stats": {"total_documents": 1000},
        "knowledge_base_info": {"status": "ready"},
        "upload_stats": {"total_files": upload_count},
        "supported_formats": len(document_parser.get_supported_formats()["supported_types"])
    }

def generate_mock_response(user_message: str, conversation_data: dict) -> str:
    """生成模拟回复"""
    industry = conversation_data["industry"]
    expert = conversation_data["expert"]
    message_count = len([m for m in conversation_data["messages"] if m["role"] == "user"])

    # 根据行业和消息数量生成不同的回复
    if industry == "tech":
        responses = [
            f"很棒的想法！从技术角度来看，{user_message[:50]}... 这个方向确实有很大潜力。让我问您几个关键问题来更好地了解您的项目。",
            f"我注意到您提到了{user_message[:30]}... 这在技术实现上需要考虑可扩展性和安全性。您的团队在这方面有什么经验吗？",
            f"基于您的描述，我看到了这个项目的技术优势。现在让我们深入分析一下市场机会和商业模式。",
            "太好了！我已经收集了足够的信息。让我为您生成一份详细的技术创业分析报告..."
        ]
    elif industry == "finance":
        responses = [
            f"从金融科技的角度分析，{user_message[:50]}... 这个领域确实需要创新解决方案。合规性将是关键考虑因素。",
            f"您提到的{user_message[:30]}... 在金融领域需要特别注意风险控制。您对相关监管政策了解吗？",
            f"很好！基于您的回答，我能看出您对金融市场有深入理解。让我们继续探讨盈利模式。",
            "完美！现在我有足够信息为您生成金融科技项目的专业分析报告..."
        ]
    else:
        responses = [
            f"非常有趣的项目！{user_message[:50]}... 让我从专业角度为您分析一下。",
            f"您的想法很有创意。关于{user_message[:30]}... 我想了解更多细节。",
            f"基于我们的对话，我对您的项目有了更清晰的认识。让我们深入分析。",
            "太棒了！让我为您生成一份详细的行业分析报告..."
        ]

    return responses[min(message_count - 1, len(responses) - 1)]

async def generate_document_based_report(upload_result: dict) -> dict:
    """基于上传文档生成分析报告"""
    try:
        file_info = upload_result["file_info"]
        business_plan_sections = upload_result["business_plan_sections"]
        validation_result = upload_result["validation_result"]
        full_content = upload_result["full_content"]

        # 分析文档内容
        report_sections = {}

        # 1. 项目概述
        executive_summary = business_plan_sections.get("executive_summary", "")
        if executive_summary:
            report_sections["project_overview"] = f"""
基于您上传的商业计划书，我为您提取了以下项目概述：

{executive_summary[:500]}...

**文档完整性评估**: {validation_result.get('completeness_score', 0):.1f}%
"""
        else:
            report_sections["project_overview"] = "未在文档中找到明确的项目概述部分，建议补充执行摘要。"

        # 2. 市场分析
        market_analysis = business_plan_sections.get("market_analysis", "")
        if market_analysis:
            report_sections["market_analysis"] = f"""
**市场分析要点**:
{market_analysis[:400]}...

**专业建议**:
- 建议进一步细化目标客户画像
- 补充竞争对手的详细分析
- 增加市场规模的量化数据
"""
        else:
            report_sections["market_analysis"] = """
**市场分析缺失**: 未在文档中找到详细的市场分析。

**建议补充**:
- 目标市场规模和增长趋势
- 主要竞争对手分析
- 客户需求和痛点分析
- 市场进入策略
"""

        # 3. 商业模式分析
        business_model = business_plan_sections.get("business_model", "")
        if business_model:
            report_sections["business_model"] = f"""
**商业模式概述**:
{business_model[:400]}...

**模式评估**:
- ✅ 有明确的商业模式描述
- 💡 建议进一步细化收入来源
- 📊 建议补充成本结构分析
"""
        else:
            report_sections["business_model"] = """
**商业模式待完善**: 文档中商业模式描述不够详细。

**建议补充**:
- 核心价值主张
- 收入模式和定价策略
- 成本结构分析
- 盈利模式可持续性
"""

        # 4. 财务分析
        financial_projections = business_plan_sections.get("financial_projections", "")
        if financial_projections:
            report_sections["financial_analysis"] = f"""
**财务预测概况**:
{financial_projections[:400]}...

**财务建议**:
- 建议提供更详细的现金流预测
- 补充关键财务指标分析
- 增加敏感性分析
"""
        else:
            report_sections["financial_analysis"] = """
**财务预测缺失**: 未找到详细的财务预测数据。

**建议补充**:
- 3-5年收入预测
- 成本和费用预算
- 现金流分析
- 盈亏平衡点分析
"""

        # 5. 团队分析
        team = business_plan_sections.get("team", "")
        if team:
            report_sections["team_analysis"] = f"""
**团队概况**:
{team[:400]}...

**团队评估**:
- 建议补充核心成员的详细背景
- 增加团队组织架构图
- 明确股权分配方案
"""
        else:
            report_sections["team_analysis"] = """
**团队信息不足**: 未找到详细的团队介绍。

**建议补充**:
- 核心团队成员背景
- 团队技能互补性分析
- 顾问团队介绍
- 人才招聘计划
"""

        # 6. 风险分析
        risk_analysis = business_plan_sections.get("risk_analysis", "")
        if risk_analysis:
            report_sections["risk_assessment"] = f"""
**风险识别**:
{risk_analysis[:400]}...

**风险管理建议**:
- 建立风险监控机制
- 制定应急预案
- 定期评估和更新风险清单
"""
        else:
            report_sections["risk_assessment"] = """
**风险分析缺失**: 未找到系统的风险分析。

**建议补充**:
- 市场风险识别
- 技术风险评估
- 财务风险分析
- 运营风险管控
"""

        # 7. 发展建议
        implementation_plan = business_plan_sections.get("implementation_plan", "")
        if implementation_plan:
            report_sections["development_suggestions"] = f"""
**实施计划概述**:
{implementation_plan[:400]}...

**发展建议**:
- 细化里程碑和时间节点
- 明确资源需求和配置
- 建立绩效评估体系
"""
        else:
            report_sections["development_suggestions"] = """
**实施计划待完善**: 缺少详细的实施计划。

**建议制定**:
- 分阶段发展规划
- 关键里程碑设定
- 资源配置计划
- 风险应对策略
"""

        # 8. 总体评估
        overall_score = validation_result.get('completeness_score', 0)
        if overall_score >= 80:
            overall_assessment = "您的商业计划书结构完整，内容详实，具有很好的可执行性。"
        elif overall_score >= 60:
            overall_assessment = "您的商业计划书基本完整，但还有一些关键部分需要补充和完善。"
        else:
            overall_assessment = "您的商业计划书还需要大幅完善，建议按照标准结构重新组织内容。"

        report_sections["overall_assessment"] = f"""
**整体评估**: {overall_assessment}

**完整性评分**: {overall_score:.1f}/100

**优先改进建议**:
{chr(10).join([f"- {suggestion}" for suggestion in validation_result.get('suggestions', [])])}

**下一步行动**:
1. 根据分析建议完善商业计划书
2. 寻求行业专家的进一步指导
3. 准备投资人路演材料
4. 开始市场验证和用户测试
"""

        return {
            "report_type": "document_based",
            "generated_at": datetime.now().isoformat(),
            "file_name": file_info["name"],
            "analysis_sections": report_sections,
            "completeness_score": overall_score,
            "recommendations": validation_result.get('suggestions', [])
        }

    except Exception as e:
        raise Exception(f"生成文档分析报告失败: {e}")

def generate_document_analysis(parse_result: dict, business_plan_sections: dict, validation_result: dict) -> str:
    """生成文档分析摘要"""
    analysis = []

    # 文档基本信息
    file_info = parse_result.get("file_info", {})
    metadata = parse_result.get("metadata", {})

    analysis.append(f"📄 **文档分析报告**")
    analysis.append(f"- 文件格式: {metadata.get('format', '未知')}")
    analysis.append(f"- 文件大小: {file_info.get('size', 0)} 字节")

    if metadata.get('pages'):
        analysis.append(f"- 页数: {metadata['pages']} 页")
    if metadata.get('paragraphs'):
        analysis.append(f"- 段落数: {metadata['paragraphs']} 个")
    if metadata.get('tables'):
        analysis.append(f"- 表格数: {metadata['tables']} 个")

    # 内容完整性分析
    analysis.append(f"\n📊 **内容完整性评估**")
    analysis.append(f"- 完整性评分: {validation_result.get('completeness_score', 0):.1f}%")

    if validation_result.get('is_complete'):
        analysis.append("- ✅ 创业计划书结构完整")
    else:
        analysis.append("- ⚠️ 创业计划书结构不完整")
        missing = validation_result.get('missing_sections', [])
        if missing:
            analysis.append(f"- 缺失部分: {', '.join(missing)}")

    # 建议
    suggestions = validation_result.get('suggestions', [])
    if suggestions:
        analysis.append(f"\n💡 **改进建议**")
        for suggestion in suggestions:
            analysis.append(f"- {suggestion}")

    return "\n".join(analysis)

def get_guided_analysis_questions(industry: str) -> dict:
    """获取引导式分析问题"""
    base_questions = {
        "basic_info": [
            {
                "id": "project_name",
                "question": "请告诉我您的项目名称和核心业务是什么？",
                "placeholder": "例如：智能客服系统，为中小企业提供AI客服解决方案"
            },
            {
                "id": "target_customers",
                "question": "您的目标客户群体是谁？他们有什么特点？",
                "placeholder": "例如：年营业额100万-1000万的电商企业，需要24小时客服但成本有限"
            },
            {
                "id": "core_problem",
                "question": "您的产品/服务解决了客户的什么核心问题？",
                "placeholder": "例如：解决中小企业客服成本高、响应慢、服务不一致的问题"
            }
        ],
        "market_analysis": [
            {
                "id": "market_size",
                "question": "您认为您所在市场的规模有多大？增长趋势如何？",
                "placeholder": "例如：中国智能客服市场规模约50亿元，年增长率25%"
            },
            {
                "id": "competitors",
                "question": "您的主要竞争对手有哪些？您的差异化优势是什么？",
                "placeholder": "例如：主要竞争对手是智齿科技、环信，我们的优势是AI算法更智能、成本更低"
            },
            {
                "id": "market_entry",
                "question": "您计划如何进入市场？有什么特殊的渠道或策略吗？",
                "placeholder": "例如：通过行业展会、合作伙伴推荐、线上营销等方式获客"
            }
        ],
        "business_model": [
            {
                "id": "revenue_model",
                "question": "您的盈利模式是什么？如何定价？",
                "placeholder": "例如：SaaS订阅模式，基础版99元/月，专业版299元/月"
            },
            {
                "id": "cost_structure",
                "question": "您的主要成本构成是什么？",
                "placeholder": "例如：技术开发40%、服务器运营20%、市场推广25%、人员成本15%"
            },
            {
                "id": "break_even",
                "question": "您预计多长时间能实现盈亏平衡？需要多少用户？",
                "placeholder": "例如：预计12个月实现盈亏平衡，需要1000个付费用户"
            }
        ],
        "team_funding": [
            {
                "id": "team_background",
                "question": "请介绍一下您的核心团队背景和优势？",
                "placeholder": "例如：CEO有10年互联网经验，CTO是AI算法专家，团队15人"
            },
            {
                "id": "funding_needs",
                "question": "您需要多少资金？主要用于哪些方面？",
                "placeholder": "例如：需要500万元，60%用于产品开发，30%用于市场推广"
            },
            {
                "id": "milestones",
                "question": "您的关键发展里程碑是什么？",
                "placeholder": "例如：6个月完成MVP，12个月获得1000用户，18个月完成A轮融资"
            }
        ]
    }

    # 根据行业添加特定问题
    industry_specific = {
        "tech": {
            "technical_details": [
                {
                    "id": "tech_innovation",
                    "question": "您的技术创新点是什么？有什么技术壁垒？",
                    "placeholder": "例如：独有的NLP算法，准确率比竞品高15%"
                },
                {
                    "id": "scalability",
                    "question": "您的技术方案可扩展性如何？能支持多大规模？",
                    "placeholder": "例如：云原生架构，可支持百万级并发用户"
                }
            ]
        },
        "finance": {
            "compliance": [
                {
                    "id": "regulatory",
                    "question": "您了解相关金融监管要求吗？如何确保合规？",
                    "placeholder": "例如：已咨询律师，正在申请相关金融牌照"
                },
                {
                    "id": "risk_control",
                    "question": "您的风险控制措施有哪些？",
                    "placeholder": "例如：多层风控模型，实时监控异常交易"
                }
            ]
        },
        "healthcare": {
            "clinical": [
                {
                    "id": "clinical_validation",
                    "question": "您的产品需要临床验证吗？进展如何？",
                    "placeholder": "例如：正在进行二期临床试验，预计明年完成"
                },
                {
                    "id": "regulatory_approval",
                    "question": "需要哪些监管部门的批准？时间计划如何？",
                    "placeholder": "例如：需要NMPA批准，预计18个月获得注册证"
                }
            ]
        }
    }

    # 合并基础问题和行业特定问题
    questions = base_questions.copy()
    if industry in industry_specific:
        questions.update(industry_specific[industry])

    return questions

async def generate_guided_analysis_report(conversation_data: dict) -> dict:
    """基于引导式问答生成分析报告"""
    try:
        industry = conversation_data["industry"]
        expert = conversation_data["expert"]
        responses = conversation_data["responses"]

        # 整理回答内容
        answers = {}
        for response in responses:
            answers[response["question_id"]] = response["answer"]

        # 生成报告各部分
        report_sections = {}

        # 1. 项目概述
        project_name = answers.get("project_name", "未提供")
        target_customers = answers.get("target_customers", "未明确")
        core_problem = answers.get("core_problem", "未明确")

        report_sections["project_overview"] = f"""
**项目名称**: {project_name}

**目标客户**: {target_customers}

**核心价值**: {core_problem}

**行业定位**: {industry}行业的创新解决方案
"""

        # 2. 市场分析
        market_size = answers.get("market_size", "未提供具体数据")
        competitors = answers.get("competitors", "未详细分析")
        market_entry = answers.get("market_entry", "未明确策略")

        report_sections["market_analysis"] = f"""
**市场规模**: {market_size}

**竞争分析**: {competitors}

**市场进入策略**: {market_entry}

**专业评估**: 基于您的描述，建议进一步细化市场调研数据，明确竞争优势的量化指标。
"""

        # 3. 商业模式
        revenue_model = answers.get("revenue_model", "未明确")
        cost_structure = answers.get("cost_structure", "未详细分析")
        break_even = answers.get("break_even", "未预测")

        report_sections["business_model"] = f"""
**盈利模式**: {revenue_model}

**成本结构**: {cost_structure}

**盈亏平衡**: {break_even}

**模式评估**: 建议进一步优化定价策略，确保单位经济效益为正。
"""

        # 4. 团队与资金
        team_background = answers.get("team_background", "未详细介绍")
        funding_needs = answers.get("funding_needs", "未明确")
        milestones = answers.get("milestones", "未制定")

        report_sections["team_funding"] = f"""
**团队背景**: {team_background}

**资金需求**: {funding_needs}

**发展里程碑**: {milestones}

**团队评估**: 建议补充核心团队成员的详细履历，明确股权激励方案。
"""

        # 5. 行业特定分析
        if industry == "tech":
            tech_innovation = answers.get("tech_innovation", "未详细说明")
            scalability = answers.get("scalability", "未评估")

            report_sections["technical_analysis"] = f"""
**技术创新**: {tech_innovation}

**可扩展性**: {scalability}

**技术建议**: 建议申请核心技术专利，建立技术壁垒。
"""
        elif industry == "finance":
            regulatory = answers.get("regulatory", "未充分考虑")
            risk_control = answers.get("risk_control", "未详细规划")

            report_sections["compliance_analysis"] = f"""
**合规情况**: {regulatory}

**风控措施**: {risk_control}

**合规建议**: 建议尽早与监管部门沟通，确保业务合规性。
"""
        elif industry == "healthcare":
            clinical_validation = answers.get("clinical_validation", "未开始")
            regulatory_approval = answers.get("regulatory_approval", "未规划")

            report_sections["clinical_analysis"] = f"""
**临床验证**: {clinical_validation}

**监管审批**: {regulatory_approval}

**医疗建议**: 建议与权威医疗机构合作，加速临床验证进程。
"""

        # 6. 综合评估和建议
        strengths = []
        weaknesses = []
        recommendations = []

        # 基于回答质量评估
        if len(answers.get("project_name", "")) > 10:
            strengths.append("项目定位清晰")
        else:
            weaknesses.append("项目定位需要进一步明确")
            recommendations.append("完善项目定位和价值主张")

        if "竞争对手" in answers.get("competitors", ""):
            strengths.append("有竞争意识")
        else:
            weaknesses.append("竞争分析不足")
            recommendations.append("深入分析主要竞争对手")

        if "万" in answers.get("funding_needs", "") or "元" in answers.get("funding_needs", ""):
            strengths.append("有明确的资金规划")
        else:
            weaknesses.append("资金需求不明确")
            recommendations.append("制定详细的资金使用计划")

        report_sections["comprehensive_assessment"] = f"""
**项目优势**:
{chr(10).join([f"- {s}" for s in strengths]) if strengths else "- 需要进一步挖掘项目优势"}

**待改进方面**:
{chr(10).join([f"- {w}" for w in weaknesses]) if weaknesses else "- 项目整体规划较为完善"}

**专业建议**:
{chr(10).join([f"- {r}" for r in recommendations]) if recommendations else "- 继续完善商业计划书细节"}

**下一步行动**:
1. 完善商业计划书文档
2. 进行市场验证和用户调研
3. 寻找合适的投资人或合作伙伴
4. 制定详细的执行计划
"""

        return {
            "report_type": "guided_analysis",
            "generated_at": datetime.now().isoformat(),
            "expert": expert["name"],
            "industry": industry,
            "analysis_sections": report_sections,
            "total_responses": len(responses),
            "completion_rate": 100
        }

    except Exception as e:
        raise Exception(f"生成引导式分析报告失败: {e}")

def generate_follow_up_message(answer: str, conversation_data: dict) -> str:
    """生成跟进消息"""
    expert_name = conversation_data["expert"]["name"]

    # 简单的回应生成
    if len(answer) > 50:
        return f"很详细的回答！{expert_name}已经记录下来了。让我们继续下一个问题。"
    elif len(answer) > 20:
        return f"不错的想法！{expert_name}建议您可以进一步展开这个点。"
    else:
        return f"感谢您的回答！{expert_name}希望了解更多细节。"

def generate_mock_analysis_report(conversation_data: dict) -> dict:
    """生成模拟分析报告"""
    industry = conversation_data["industry"]
    expert = conversation_data["expert"]

    report_text = f"""
# 创业项目分析报告

## 1. 项目概述
基于我们的深入交流，您的{industry}领域创业项目展现出了很大的潜力。项目具有明确的市场定位和创新价值。

## 2. 市场分析
**市场机会**: {industry}行业正处于快速发展期，市场需求旺盛。
**竞争格局**: 虽然竞争激烈，但仍有细分市场机会。
**目标客户**: 您的目标客户群体定位清晰，具有较强的付费意愿。

## 3. 商业模式评估
**盈利模式**: 建议采用多元化收入结构，降低单一收入来源风险。
**成本结构**: 需要重点关注获客成本和运营成本的控制。
**扩展性**: 项目具备良好的可扩展性，适合规模化发展。

## 4. 风险分析
**主要风险**:
- 市场竞争加剧
- 技术迭代风险
- 资金链风险
- 团队稳定性风险

**应对策略**:
- 建立技术壁垒
- 多元化融资渠道
- 完善团队激励机制

## 5. 发展建议
1. **短期目标**: 专注产品打磨，获得种子用户验证
2. **中期目标**: 扩大市场份额，建立品牌影响力
3. **长期目标**: 成为行业领导者，考虑国际化发展

## 6. 行业预测
{industry}行业在未来3-5年将继续保持高速增长，预计年增长率达到20-30%。新技术的应用将带来更多创新机会。

## 7. 下一步行动计划
1. 完善商业计划书
2. 寻找合适的投资人
3. 组建核心团队
4. 开发MVP产品
5. 进行市场测试

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*分析专家: {expert['name']}*
"""

    return {
        "report_text": report_text,
        "generated_at": datetime.now().isoformat(),
        "expert": expert["name"],
        "industry": industry
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
