"""
创业分析智能体主程序
"""
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request
from fastapi.responses import HTMLResponse, JSONResponse
import uvicorn
from loguru import logger
import os

from config import config
from src.database.db_manager import DatabaseManager
from src.rag.rag_system import RAGSystem
from src.agent.startup_agent import StartupAgent

# 创建FastAPI应用
app = FastAPI(title="创业分析智能体", description="基于RAG技术的U30创业者智能分析助手")

# 静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 全局变量
db_manager = None
rag_system = None
startup_agent = None

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    global db_manager, rag_system, startup_agent
    
    logger.info("正在初始化创业分析智能体...")
    
    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        logger.info("数据库连接已建立")
        
        # 初始化RAG系统
        rag_system = RAGSystem()
        await rag_system.initialize()
        logger.info("RAG系统初始化完成")
        
        # 初始化智能体
        startup_agent = StartupAgent(rag_system)
        logger.info("创业分析智能体初始化完成")
        
    except Exception as e:
        logger.error(f"初始化失败: {e}")
        raise

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """主页"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/industries")
async def get_industries():
    """获取支持的行业列表"""
    industries = [
        {"id": "tech", "name": "科技互联网", "description": "人工智能、软件开发、互联网平台等"},
        {"id": "finance", "name": "金融科技", "description": "数字支付、区块链、投资理财等"},
        {"id": "healthcare", "name": "医疗健康", "description": "生物医药、医疗器械、健康管理等"},
        {"id": "education", "name": "教育培训", "description": "在线教育、职业培训、知识付费等"},
        {"id": "retail", "name": "新零售", "description": "电商平台、社交电商、智能零售等"},
        {"id": "manufacturing", "name": "智能制造", "description": "工业4.0、智能工厂、自动化设备等"},
        {"id": "energy", "name": "新能源", "description": "太阳能、风能、储能技术等"},
        {"id": "agriculture", "name": "智慧农业", "description": "农业科技、精准农业、农产品电商等"}
    ]
    return {"industries": industries}

@app.post("/chat/start")
async def start_chat(industry: str = Form(...)):
    """开始对话"""
    try:
        result = await startup_agent.start_conversation(industry)
        return result
    except Exception as e:
        logger.error(f"开始对话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat/message")
async def send_message(message: str = Form(...), message_type: str = Form("text")):
    """发送消息"""
    try:
        result = await startup_agent.process_user_input(message, message_type)
        return result
    except Exception as e:
        logger.error(f"处理消息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/upload/business-plan")
async def upload_business_plan(file: UploadFile = File(...)):
    """上传创业计划书"""
    try:
        # 检查文件类型
        allowed_types = ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text/plain"]
        if file.content_type not in allowed_types:
            raise HTTPException(status_code=400, detail="不支持的文件类型")

        # 保存文件
        file_path = f"uploads/{file.filename}"
        os.makedirs("uploads", exist_ok=True)

        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # 解析文件内容（这里简化处理）
        file_content = "文件上传成功，内容解析功能待实现"

        return {
            "success": True,
            "message": "文件上传成功",
            "file_content": file_content
        }

    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/chat/history")
async def get_chat_history():
    """获取对话历史"""
    try:
        history = startup_agent.get_conversation_history()
        return {"history": history}
    except Exception as e:
        logger.error(f"获取对话历史失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/system/stats")
async def get_system_stats():
    """获取系统统计信息"""
    try:
        stats = rag_system.get_system_stats()
        return stats
    except Exception as e:
        logger.error(f"获取系统统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=config.APP_HOST,
        port=config.APP_PORT,
        reload=config.DEBUG
    )
