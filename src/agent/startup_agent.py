"""
创业分析智能体
提供专业的创业分析和建议
"""
import asyncio
from typing import Dict, Any, List, Optional
from loguru import logger
import json
from datetime import datetime

from src.rag.rag_system import RAGSystem
from .industry_experts import get_industry_expert, IndustryExpert


class StartupAgent:
    """创业分析智能体"""
    
    def __init__(self, rag_system: RAGSystem):
        self.rag_system = rag_system
        self.current_expert = None
        self.conversation_history = []
        self.user_profile = {}
        self.analysis_state = "initial"  # initial, questioning, analyzing, completed
    
    async def start_conversation(self, industry_type: str = "tech") -> Dict[str, Any]:
        """开始对话"""
        try:
            # 设置行业专家
            self.current_expert = get_industry_expert(industry_type)
            self.analysis_state = "initial"
            self.conversation_history = []
            
            # 获取欢迎消息
            welcome_message = await self._generate_welcome_message()
            
            # 获取初始问题
            questions = self.current_expert.get_analysis_questions()
            
            return {
                "success": True,
                "expert_name": self.current_expert.name,
                "expert_personality": self.current_expert.personality,
                "welcome_message": welcome_message,
                "initial_questions": questions.get("basic_info", []),
                "analysis_state": self.analysis_state
            }
            
        except Exception as e:
            logger.error(f"开始对话失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _generate_welcome_message(self) -> str:
        """生成欢迎消息"""
        try:
            system_prompt = self.current_expert.get_system_prompt()
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": "请生成一个热情友好的欢迎消息，介绍自己并说明你能为创业者提供什么帮助。"}
            ]
            
            welcome = await self.rag_system.qwen_client.chat_completion(messages, temperature=0.8)
            return welcome
            
        except Exception as e:
            logger.error(f"生成欢迎消息失败: {e}")
            return f"你好！我是{self.current_expert.name}，很高兴为您提供创业分析服务！"
    
    async def process_user_input(self, user_input: str, input_type: str = "text") -> Dict[str, Any]:
        """处理用户输入"""
        try:
            # 记录对话历史
            self.conversation_history.append({
                "timestamp": datetime.now().isoformat(),
                "role": "user",
                "content": user_input,
                "type": input_type
            })
            
            # 根据当前状态处理输入
            if self.analysis_state == "initial":
                response = await self._handle_initial_input(user_input)
            elif self.analysis_state == "questioning":
                response = await self._handle_questioning_input(user_input)
            elif self.analysis_state == "analyzing":
                response = await self._handle_analysis_input(user_input)
            else:
                response = await self._handle_general_input(user_input)
            
            # 记录助手回复
            self.conversation_history.append({
                "timestamp": datetime.now().isoformat(),
                "role": "assistant",
                "content": response.get("message", ""),
                "type": "response"
            })
            
            return response
            
        except Exception as e:
            logger.error(f"处理用户输入失败: {e}")
            return {
                "success": False,
                "message": "抱歉，我遇到了一些问题，请稍后再试。",
                "error": str(e)
            }
    
    async def _handle_initial_input(self, user_input: str) -> Dict[str, Any]:
        """处理初始输入"""
        # 提取用户基本信息
        await self._extract_user_profile(user_input)
        
        # 生成回复并提供更多问题
        system_prompt = self.current_expert.get_system_prompt()
        
        # 搜索相关知识
        relevant_docs = await self.rag_system.search(user_input, top_k=5)
        context = self.rag_system._build_context(relevant_docs, max_length=2000)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"""
用户刚刚分享了他们的创业想法：
{user_input}

相关行业信息：
{context}

请：
1. 对用户的想法给出初步的专业反馈
2. 提出2-3个深入的问题来更好地了解项目
3. 保持鼓励和专业的语调
"""}
        ]
        
        response_text = await self.rag_system.qwen_client.chat_completion(messages, temperature=0.7)
        
        # 获取下一阶段问题
        questions = self.current_expert.get_analysis_questions()
        next_questions = questions.get("market_analysis", [])
        
        self.analysis_state = "questioning"
        
        return {
            "success": True,
            "message": response_text,
            "next_questions": next_questions,
            "analysis_state": self.analysis_state,
            "progress": 25
        }
    
    async def _handle_questioning_input(self, user_input: str) -> Dict[str, Any]:
        """处理问答阶段输入"""
        # 更新用户档案
        await self._update_user_profile(user_input)
        
        # 检查是否收集了足够信息
        if len(self.conversation_history) >= 8:  # 至少4轮对话
            return await self._start_analysis()
        
        # 继续提问
        system_prompt = self.current_expert.get_system_prompt()
        
        # 搜索相关信息
        relevant_docs = await self.rag_system.search(user_input, top_k=3)
        context = self.rag_system._build_context(relevant_docs, max_length=1500)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"""
基于用户的回答：
{user_input}

相关行业信息：
{context}

请：
1. 对用户的回答给出专业点评
2. 提出1-2个更深入的问题
3. 如果信息足够，可以开始分析阶段
"""}
        ]
        
        response_text = await self.rag_system.qwen_client.chat_completion(messages, temperature=0.7)
        
        # 获取商业模式相关问题
        questions = self.current_expert.get_analysis_questions()
        next_questions = questions.get("business_model", [])
        
        return {
            "success": True,
            "message": response_text,
            "next_questions": next_questions[:2],  # 限制问题数量
            "analysis_state": self.analysis_state,
            "progress": 50
        }
    
    async def _start_analysis(self) -> Dict[str, Any]:
        """开始深度分析"""
        self.analysis_state = "analyzing"
        
        # 生成综合分析报告
        analysis_report = await self._generate_analysis_report()
        
        return {
            "success": True,
            "message": "太好了！基于我们的对话，我已经收集了足够的信息。让我为您生成一份详细的分析报告...",
            "analysis_report": analysis_report,
            "analysis_state": self.analysis_state,
            "progress": 75
        }
    
    async def _generate_analysis_report(self) -> Dict[str, Any]:
        """生成分析报告"""
        try:
            # 整理对话内容
            conversation_summary = self._summarize_conversation()
            
            # 搜索相关行业数据
            search_queries = [
                f"{self.current_expert.industry_type.value} 行业趋势",
                f"{self.current_expert.industry_type.value} 创业机会",
                f"{self.current_expert.industry_type.value} 风险分析"
            ]
            
            all_relevant_docs = []
            for query in search_queries:
                docs = await self.rag_system.search(query, top_k=3)
                all_relevant_docs.extend(docs)
            
            context = self.rag_system._build_context(all_relevant_docs, max_length=3000)
            
            # 生成分析报告
            system_prompt = self.current_expert.get_system_prompt()
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"""
基于以下对话内容和行业信息，请生成一份详细的创业分析报告：

对话总结：
{conversation_summary}

行业相关信息：
{context}

请按以下结构生成报告：
1. 项目概述
2. 市场分析
3. 商业模式评估
4. 风险分析
5. 发展建议
6. 行业预测
7. 下一步行动计划

每个部分都要详细、专业，并提供具体可行的建议。
"""}
            ]
            
            report_text = await self.rag_system.qwen_client.chat_completion(
                messages, 
                temperature=0.6, 
                max_tokens=3000
            )
            
            return {
                "report_text": report_text,
                "generated_at": datetime.now().isoformat(),
                "expert": self.current_expert.name,
                "industry": self.current_expert.industry_type.value
            }
            
        except Exception as e:
            logger.error(f"生成分析报告失败: {e}")
            return {
                "report_text": "抱歉，生成分析报告时遇到了问题。请稍后再试。",
                "error": str(e)
            }
    
    def _summarize_conversation(self) -> str:
        """总结对话内容"""
        user_messages = [
            msg["content"] for msg in self.conversation_history 
            if msg["role"] == "user"
        ]
        return "\n".join([f"- {msg}" for msg in user_messages])
    
    async def _extract_user_profile(self, user_input: str):
        """提取用户档案信息"""
        # 简单的关键词提取（实际应用中可以使用NLP技术）
        self.user_profile["initial_idea"] = user_input
        self.user_profile["timestamp"] = datetime.now().isoformat()
    
    async def _update_user_profile(self, user_input: str):
        """更新用户档案"""
        if "responses" not in self.user_profile:
            self.user_profile["responses"] = []
        
        self.user_profile["responses"].append({
            "content": user_input,
            "timestamp": datetime.now().isoformat()
        })
    
    async def _handle_analysis_input(self, user_input: str) -> Dict[str, Any]:
        """处理分析阶段输入"""
        # 用户可能对分析报告有问题或需要更多信息
        return await self._handle_general_input(user_input)
    
    async def _handle_general_input(self, user_input: str) -> Dict[str, Any]:
        """处理一般输入"""
        system_prompt = self.current_expert.get_system_prompt()
        
        # 搜索相关信息
        relevant_docs = await self.rag_system.search(user_input, top_k=5)
        
        # 生成回复
        response_text = await self.rag_system.generate_response(
            user_input, 
            relevant_docs, 
            system_prompt
        )
        
        return {
            "success": True,
            "message": response_text,
            "analysis_state": self.analysis_state,
            "progress": 100 if self.analysis_state == "analyzing" else 75
        }
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        return self.conversation_history
    
    def get_user_profile(self) -> Dict[str, Any]:
        """获取用户档案"""
        return self.user_profile
