"""
文档解析工具
支持PDF、Word、文本文件的解析
"""
import os
from typing import Dict, Any, Optional
from loguru import logger
import PyPDF2
from docx import Document
import mimetypes


class DocumentParser:
    """文档解析器"""
    
    def __init__(self):
        self.supported_types = {
            'application/pdf': self._parse_pdf,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': self._parse_docx,
            'text/plain': self._parse_text
        }
    
    def parse_file(self, file_path: str) -> Dict[str, Any]:
        """解析文件"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 检测文件类型
            mime_type, _ = mimetypes.guess_type(file_path)
            
            if mime_type not in self.supported_types:
                raise ValueError(f"不支持的文件类型: {mime_type}")
            
            # 解析文件
            parser_func = self.supported_types[mime_type]
            content = parser_func(file_path)
            
            # 获取文件信息
            file_stats = os.stat(file_path)
            
            return {
                'success': True,
                'content': content,
                'file_info': {
                    'name': os.path.basename(file_path),
                    'size': file_stats.st_size,
                    'type': mime_type,
                    'path': file_path
                }
            }
            
        except Exception as e:
            logger.error(f"解析文件失败 {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'content': '',
                'file_info': {}
            }
    
    def _parse_pdf(self, file_path: str) -> str:
        """解析PDF文件"""
        try:
            content = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    content += page.extract_text() + "\n"
            
            return content.strip()
            
        except Exception as e:
            logger.error(f"解析PDF失败: {e}")
            raise
    
    def _parse_docx(self, file_path: str) -> str:
        """解析Word文档"""
        try:
            doc = Document(file_path)
            content = ""
            
            for paragraph in doc.paragraphs:
                content += paragraph.text + "\n"
            
            # 解析表格
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        row_text.append(cell.text.strip())
                    content += " | ".join(row_text) + "\n"
            
            return content.strip()
            
        except Exception as e:
            logger.error(f"解析Word文档失败: {e}")
            raise
    
    def _parse_text(self, file_path: str) -> str:
        """解析文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            return content.strip()
            
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as file:
                    content = file.read()
                return content.strip()
            except Exception as e:
                logger.error(f"解析文本文件失败: {e}")
                raise
        except Exception as e:
            logger.error(f"解析文本文件失败: {e}")
            raise
    
    def extract_business_plan_sections(self, content: str) -> Dict[str, str]:
        """提取创业计划书的各个部分"""
        sections = {
            'executive_summary': '',
            'market_analysis': '',
            'business_model': '',
            'financial_projections': '',
            'team': '',
            'risk_analysis': '',
            'implementation_plan': ''
        }
        
        # 简单的关键词匹配来识别不同部分
        keywords = {
            'executive_summary': ['执行摘要', '项目概述', '概要', '摘要'],
            'market_analysis': ['市场分析', '市场调研', '行业分析', '竞争分析'],
            'business_model': ['商业模式', '盈利模式', '业务模式', '商业计划'],
            'financial_projections': ['财务预测', '财务分析', '资金需求', '投资回报'],
            'team': ['团队介绍', '管理团队', '核心团队', '人员配置'],
            'risk_analysis': ['风险分析', '风险评估', '潜在风险', '风险控制'],
            'implementation_plan': ['实施计划', '执行计划', '发展规划', '时间安排']
        }
        
        lines = content.split('\n')
        current_section = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否是新的部分标题
            for section, section_keywords in keywords.items():
                if any(keyword in line for keyword in section_keywords):
                    current_section = section
                    break
            
            # 将内容添加到当前部分
            if current_section:
                sections[current_section] += line + '\n'
        
        # 清理空白内容
        for section in sections:
            sections[section] = sections[section].strip()
        
        return sections
    
    def validate_business_plan(self, sections: Dict[str, str]) -> Dict[str, Any]:
        """验证创业计划书的完整性"""
        validation_result = {
            'is_complete': True,
            'missing_sections': [],
            'completeness_score': 0,
            'suggestions': []
        }
        
        required_sections = ['executive_summary', 'market_analysis', 'business_model']
        important_sections = ['financial_projections', 'team', 'risk_analysis']
        
        # 检查必需部分
        for section in required_sections:
            if not sections.get(section):
                validation_result['missing_sections'].append(section)
                validation_result['is_complete'] = False
        
        # 检查重要部分
        for section in important_sections:
            if not sections.get(section):
                validation_result['missing_sections'].append(section)
        
        # 计算完整性分数
        total_sections = len(sections)
        filled_sections = sum(1 for content in sections.values() if content.strip())
        validation_result['completeness_score'] = (filled_sections / total_sections) * 100
        
        # 生成建议
        if validation_result['missing_sections']:
            validation_result['suggestions'].append(
                f"建议补充以下部分: {', '.join(validation_result['missing_sections'])}"
            )
        
        if validation_result['completeness_score'] < 70:
            validation_result['suggestions'].append(
                "创业计划书内容较少，建议增加更多详细信息"
            )
        
        return validation_result
