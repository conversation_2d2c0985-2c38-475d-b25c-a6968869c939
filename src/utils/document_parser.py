"""
增强版文档解析工具
支持PDF、Word、Markdown、Excel、PowerPoint等多种格式
"""
import os
import re
from typing import Dict, Any, Optional, List
import mimetypes
import json
from datetime import datetime

try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import markdown
    MARKDOWN_AVAILABLE = True
except ImportError:
    MARKDOWN_AVAILABLE = False

try:
    import openpyxl
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

try:
    from pptx import Presentation
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False


class EnhancedDocumentParser:
    """增强版文档解析器"""

    def __init__(self):
        self.supported_types = {
            'application/pdf': self._parse_pdf,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': self._parse_docx,
            'application/msword': self._parse_doc_fallback,
            'text/plain': self._parse_text,
            'text/markdown': self._parse_markdown,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': self._parse_excel,
            'application/vnd.ms-excel': self._parse_excel_fallback,
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': self._parse_pptx,
            'application/vnd.ms-powerpoint': self._parse_ppt_fallback,
            'application/json': self._parse_json,
            'text/csv': self._parse_csv,
            'text/html': self._parse_html
        }

        # 文件扩展名映射
        self.extension_mapping = {
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.doc': 'application/msword',
            '.txt': 'text/plain',
            '.md': 'text/markdown',
            '.markdown': 'text/markdown',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.xls': 'application/vnd.ms-excel',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.ppt': 'application/vnd.ms-powerpoint',
            '.json': 'application/json',
            '.csv': 'text/csv',
            '.html': 'text/html',
            '.htm': 'text/html'
        }
    
    def parse_file(self, file_path: str) -> Dict[str, Any]:
        """解析文件"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 检测文件类型
            mime_type = self._detect_file_type(file_path)

            if mime_type not in self.supported_types:
                raise ValueError(f"不支持的文件类型: {mime_type}")

            # 检查依赖是否可用
            if not self._check_parser_availability(mime_type):
                raise ValueError(f"缺少解析 {mime_type} 所需的依赖库")

            # 解析文件
            parser_func = self.supported_types[mime_type]
            parsed_result = parser_func(file_path)

            # 获取文件信息
            file_stats = os.stat(file_path)

            # 如果解析结果是字符串，转换为标准格式
            if isinstance(parsed_result, str):
                content = parsed_result
                metadata = {}
            else:
                content = parsed_result.get('content', '')
                metadata = parsed_result.get('metadata', {})

            return {
                'success': True,
                'content': content,
                'metadata': metadata,
                'file_info': {
                    'name': os.path.basename(file_path),
                    'size': file_stats.st_size,
                    'type': mime_type,
                    'path': file_path,
                    'parsed_at': datetime.now().isoformat()
                }
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'content': '',
                'metadata': {},
                'file_info': {}
            }

    def _detect_file_type(self, file_path: str) -> str:
        """检测文件类型"""
        # 首先尝试通过扩展名检测
        _, ext = os.path.splitext(file_path.lower())
        if ext in self.extension_mapping:
            return self.extension_mapping[ext]

        # 然后使用mimetypes检测
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type:
            return mime_type

        # 默认为文本文件
        return 'text/plain'

    def _check_parser_availability(self, mime_type: str) -> bool:
        """检查解析器依赖是否可用"""
        if mime_type == 'application/pdf':
            return PDF_AVAILABLE
        elif 'word' in mime_type or 'document' in mime_type:
            return DOCX_AVAILABLE
        elif mime_type == 'text/markdown':
            return MARKDOWN_AVAILABLE
        elif 'spreadsheet' in mime_type or 'excel' in mime_type:
            return EXCEL_AVAILABLE
        elif 'presentation' in mime_type or 'powerpoint' in mime_type:
            return PPTX_AVAILABLE
        else:
            return True  # 文本文件等基础格式总是可用
    
    def _parse_pdf(self, file_path: str) -> Dict[str, Any]:
        """解析PDF文件"""
        try:
            content = ""
            metadata = {"pages": 0, "format": "PDF"}

            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                metadata["pages"] = len(pdf_reader.pages)

                # 提取PDF元数据
                if pdf_reader.metadata:
                    metadata.update({
                        "title": pdf_reader.metadata.get('/Title', ''),
                        "author": pdf_reader.metadata.get('/Author', ''),
                        "subject": pdf_reader.metadata.get('/Subject', ''),
                        "creator": pdf_reader.metadata.get('/Creator', '')
                    })

                # 提取文本内容
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    page_text = page.extract_text()
                    if page_text.strip():
                        content += f"\n--- 第{page_num + 1}页 ---\n"
                        content += page_text + "\n"

            return {
                "content": content.strip(),
                "metadata": metadata
            }

        except Exception as e:
            raise Exception(f"解析PDF失败: {e}")

    def _parse_markdown(self, file_path: str) -> Dict[str, Any]:
        """解析Markdown文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                md_content = file.read()

            # 转换为HTML然后提取纯文本
            if MARKDOWN_AVAILABLE:
                html = markdown.markdown(md_content)
                # 简单的HTML标签移除
                import re
                text_content = re.sub(r'<[^>]+>', '', html)
                text_content = re.sub(r'\s+', ' ', text_content).strip()
            else:
                text_content = md_content

            # 提取标题结构
            headers = re.findall(r'^(#{1,6})\s+(.+)$', md_content, re.MULTILINE)

            metadata = {
                "format": "Markdown",
                "headers": [{"level": len(h[0]), "text": h[1]} for h in headers],
                "original_markdown": md_content
            }

            return {
                "content": text_content,
                "metadata": metadata
            }

        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as file:
                    content = file.read()
                return {"content": content, "metadata": {"format": "Markdown", "encoding": "gbk"}}
            except Exception as e:
                raise Exception(f"解析Markdown文件失败: {e}")
        except Exception as e:
            raise Exception(f"解析Markdown文件失败: {e}")
    
    def _parse_docx(self, file_path: str) -> Dict[str, Any]:
        """解析Word文档"""
        try:
            doc = Document(file_path)
            content = ""
            metadata = {"format": "Word Document", "paragraphs": 0, "tables": 0}

            # 提取文档属性
            core_props = doc.core_properties
            metadata.update({
                "title": core_props.title or "",
                "author": core_props.author or "",
                "subject": core_props.subject or "",
                "created": str(core_props.created) if core_props.created else "",
                "modified": str(core_props.modified) if core_props.modified else ""
            })

            # 解析段落
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content += paragraph.text + "\n"
                    metadata["paragraphs"] += 1

            # 解析表格
            for table in doc.tables:
                content += "\n--- 表格 ---\n"
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_text.append(cell_text)
                    if row_text:
                        content += " | ".join(row_text) + "\n"
                metadata["tables"] += 1

            return {
                "content": content.strip(),
                "metadata": metadata
            }

        except Exception as e:
            raise Exception(f"解析Word文档失败: {e}")

    def _parse_excel(self, file_path: str) -> Dict[str, Any]:
        """解析Excel文件"""
        try:
            workbook = openpyxl.load_workbook(file_path, read_only=True)
            content = ""
            metadata = {"format": "Excel", "sheets": [], "total_rows": 0}

            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                content += f"\n--- 工作表: {sheet_name} ---\n"

                sheet_data = []
                row_count = 0

                for row in sheet.iter_rows(values_only=True):
                    if any(cell is not None for cell in row):
                        row_data = [str(cell) if cell is not None else "" for cell in row]
                        sheet_data.append(row_data)
                        content += " | ".join(row_data) + "\n"
                        row_count += 1

                metadata["sheets"].append({
                    "name": sheet_name,
                    "rows": row_count
                })
                metadata["total_rows"] += row_count

            workbook.close()

            return {
                "content": content.strip(),
                "metadata": metadata
            }

        except Exception as e:
            raise Exception(f"解析Excel文件失败: {e}")

    def _parse_pptx(self, file_path: str) -> Dict[str, Any]:
        """解析PowerPoint文件"""
        try:
            prs = Presentation(file_path)
            content = ""
            metadata = {"format": "PowerPoint", "slides": 0}

            for i, slide in enumerate(prs.slides, 1):
                content += f"\n--- 幻灯片 {i} ---\n"

                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        content += shape.text + "\n"

                metadata["slides"] += 1

            return {
                "content": content.strip(),
                "metadata": metadata
            }

        except Exception as e:
            raise Exception(f"解析PowerPoint文件失败: {e}")
    
    def _parse_text(self, file_path: str) -> Dict[str, Any]:
        """解析文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()

            metadata = {
                "format": "Text",
                "encoding": "utf-8",
                "lines": len(content.split('\n')),
                "characters": len(content)
            }

            return {
                "content": content.strip(),
                "metadata": metadata
            }

        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as file:
                    content = file.read()

                metadata = {
                    "format": "Text",
                    "encoding": "gbk",
                    "lines": len(content.split('\n')),
                    "characters": len(content)
                }

                return {
                    "content": content.strip(),
                    "metadata": metadata
                }
            except Exception as e:
                raise Exception(f"解析文本文件失败: {e}")
        except Exception as e:
            raise Exception(f"解析文本文件失败: {e}")

    def _parse_json(self, file_path: str) -> Dict[str, Any]:
        """解析JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)

            # 将JSON转换为可读文本
            content = json.dumps(data, ensure_ascii=False, indent=2)

            metadata = {
                "format": "JSON",
                "structure": type(data).__name__,
                "keys": list(data.keys()) if isinstance(data, dict) else None
            }

            return {
                "content": content,
                "metadata": metadata
            }

        except Exception as e:
            raise Exception(f"解析JSON文件失败: {e}")

    def _parse_csv(self, file_path: str) -> Dict[str, Any]:
        """解析CSV文件"""
        try:
            import csv
            content = ""
            metadata = {"format": "CSV", "rows": 0, "columns": 0}

            with open(file_path, 'r', encoding='utf-8') as file:
                csv_reader = csv.reader(file)
                rows = list(csv_reader)

                if rows:
                    metadata["columns"] = len(rows[0])
                    metadata["rows"] = len(rows)

                    # 转换为文本格式
                    for row in rows:
                        content += " | ".join(str(cell) for cell in row) + "\n"

            return {
                "content": content.strip(),
                "metadata": metadata
            }

        except Exception as e:
            raise Exception(f"解析CSV文件失败: {e}")

    def _parse_html(self, file_path: str) -> Dict[str, Any]:
        """解析HTML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                html_content = file.read()

            # 简单的HTML标签移除
            import re
            text_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL)
            text_content = re.sub(r'<style[^>]*>.*?</style>', '', text_content, flags=re.DOTALL)
            text_content = re.sub(r'<[^>]+>', '', text_content)
            text_content = re.sub(r'\s+', ' ', text_content).strip()

            metadata = {
                "format": "HTML",
                "original_size": len(html_content),
                "text_size": len(text_content)
            }

            return {
                "content": text_content,
                "metadata": metadata
            }

        except Exception as e:
            raise Exception(f"解析HTML文件失败: {e}")

    # 回退解析方法
    def _parse_doc_fallback(self, file_path: str) -> Dict[str, Any]:
        """旧版Word文档回退解析"""
        return {
            "content": "检测到旧版Word文档(.doc)，请转换为.docx格式以获得更好的解析效果。",
            "metadata": {"format": "Word Document (Legacy)", "note": "需要转换格式"}
        }

    def _parse_excel_fallback(self, file_path: str) -> Dict[str, Any]:
        """旧版Excel文档回退解析"""
        return {
            "content": "检测到旧版Excel文档(.xls)，请转换为.xlsx格式以获得更好的解析效果。",
            "metadata": {"format": "Excel (Legacy)", "note": "需要转换格式"}
        }

    def _parse_ppt_fallback(self, file_path: str) -> Dict[str, Any]:
        """旧版PowerPoint文档回退解析"""
        return {
            "content": "检测到旧版PowerPoint文档(.ppt)，请转换为.pptx格式以获得更好的解析效果。",
            "metadata": {"format": "PowerPoint (Legacy)", "note": "需要转换格式"}
        }
    
    def extract_business_plan_sections(self, content: str) -> Dict[str, str]:
        """提取创业计划书的各个部分"""
        sections = {
            'executive_summary': '',
            'market_analysis': '',
            'business_model': '',
            'financial_projections': '',
            'team': '',
            'risk_analysis': '',
            'implementation_plan': ''
        }
        
        # 简单的关键词匹配来识别不同部分
        keywords = {
            'executive_summary': ['执行摘要', '项目概述', '概要', '摘要'],
            'market_analysis': ['市场分析', '市场调研', '行业分析', '竞争分析'],
            'business_model': ['商业模式', '盈利模式', '业务模式', '商业计划'],
            'financial_projections': ['财务预测', '财务分析', '资金需求', '投资回报'],
            'team': ['团队介绍', '管理团队', '核心团队', '人员配置'],
            'risk_analysis': ['风险分析', '风险评估', '潜在风险', '风险控制'],
            'implementation_plan': ['实施计划', '执行计划', '发展规划', '时间安排']
        }
        
        lines = content.split('\n')
        current_section = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否是新的部分标题
            for section, section_keywords in keywords.items():
                if any(keyword in line for keyword in section_keywords):
                    current_section = section
                    break
            
            # 将内容添加到当前部分
            if current_section:
                sections[current_section] += line + '\n'
        
        # 清理空白内容
        for section in sections:
            sections[section] = sections[section].strip()
        
        return sections
    
    def validate_business_plan(self, sections: Dict[str, str]) -> Dict[str, Any]:
        """验证创业计划书的完整性"""
        validation_result = {
            'is_complete': True,
            'missing_sections': [],
            'completeness_score': 0,
            'suggestions': []
        }
        
        required_sections = ['executive_summary', 'market_analysis', 'business_model']
        important_sections = ['financial_projections', 'team', 'risk_analysis']
        
        # 检查必需部分
        for section in required_sections:
            if not sections.get(section):
                validation_result['missing_sections'].append(section)
                validation_result['is_complete'] = False
        
        # 检查重要部分
        for section in important_sections:
            if not sections.get(section):
                validation_result['missing_sections'].append(section)
        
        # 计算完整性分数
        total_sections = len(sections)
        filled_sections = sum(1 for content in sections.values() if content.strip())
        validation_result['completeness_score'] = (filled_sections / total_sections) * 100
        
        # 生成建议
        if validation_result['missing_sections']:
            validation_result['suggestions'].append(
                f"建议补充以下部分: {', '.join(validation_result['missing_sections'])}"
            )
        
        if validation_result['completeness_score'] < 70:
            validation_result['suggestions'].append(
                "创业计划书内容较少，建议增加更多详细信息"
            )
        
        return validation_result

    def get_supported_formats(self) -> Dict[str, Any]:
        """获取支持的文件格式列表"""
        formats = {
            "supported_types": [],
            "dependencies_status": {
                "PDF": PDF_AVAILABLE,
                "Word": DOCX_AVAILABLE,
                "Markdown": MARKDOWN_AVAILABLE,
                "Excel": EXCEL_AVAILABLE,
                "PowerPoint": PPTX_AVAILABLE
            }
        }

        format_info = {
            'application/pdf': {
                "name": "PDF文档",
                "extensions": [".pdf"],
                "available": PDF_AVAILABLE,
                "description": "便携式文档格式"
            },
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': {
                "name": "Word文档",
                "extensions": [".docx"],
                "available": DOCX_AVAILABLE,
                "description": "Microsoft Word文档"
            },
            'application/msword': {
                "name": "Word文档(旧版)",
                "extensions": [".doc"],
                "available": False,
                "description": "Microsoft Word文档(需转换为.docx)"
            },
            'text/plain': {
                "name": "文本文件",
                "extensions": [".txt"],
                "available": True,
                "description": "纯文本文件"
            },
            'text/markdown': {
                "name": "Markdown文档",
                "extensions": [".md", ".markdown"],
                "available": MARKDOWN_AVAILABLE,
                "description": "Markdown标记语言文档"
            },
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
                "name": "Excel表格",
                "extensions": [".xlsx"],
                "available": EXCEL_AVAILABLE,
                "description": "Microsoft Excel表格"
            },
            'application/vnd.ms-excel': {
                "name": "Excel表格(旧版)",
                "extensions": [".xls"],
                "available": False,
                "description": "Microsoft Excel表格(需转换为.xlsx)"
            },
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': {
                "name": "PowerPoint演示文稿",
                "extensions": [".pptx"],
                "available": PPTX_AVAILABLE,
                "description": "Microsoft PowerPoint演示文稿"
            },
            'application/vnd.ms-powerpoint': {
                "name": "PowerPoint演示文稿(旧版)",
                "extensions": [".ppt"],
                "available": False,
                "description": "Microsoft PowerPoint演示文稿(需转换为.pptx)"
            },
            'application/json': {
                "name": "JSON文件",
                "extensions": [".json"],
                "available": True,
                "description": "JavaScript对象表示法文件"
            },
            'text/csv': {
                "name": "CSV文件",
                "extensions": [".csv"],
                "available": True,
                "description": "逗号分隔值文件"
            },
            'text/html': {
                "name": "HTML文件",
                "extensions": [".html", ".htm"],
                "available": True,
                "description": "超文本标记语言文件"
            }
        }

        formats["supported_types"] = format_info
        return formats

    def get_file_type_info(self, file_path: str) -> Dict[str, Any]:
        """获取文件类型信息"""
        mime_type = self._detect_file_type(file_path)
        formats = self.get_supported_formats()

        file_info = {
            "detected_type": mime_type,
            "is_supported": mime_type in self.supported_types,
            "parser_available": self._check_parser_availability(mime_type),
            "format_info": formats["supported_types"].get(mime_type, {})
        }

        return file_info
