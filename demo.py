#!/usr/bin/env python3
"""
创业分析智能体演示脚本
"""
import requests
import json
import time

def demo_conversation():
    """演示完整的对话流程"""
    base_url = "http://localhost:8000"
    
    print("🚀 创业分析智能体演示")
    print("=" * 50)
    
    # 1. 获取行业列表
    print("\n1. 获取支持的行业列表...")
    response = requests.get(f"{base_url}/industries")
    industries = response.json()["industries"]
    
    for i, industry in enumerate(industries, 1):
        print(f"   {i}. {industry['name']} - {industry['description']}")
    
    # 2. 选择行业并开始对话
    print("\n2. 选择科技互联网行业，开始对话...")
    response = requests.post(f"{base_url}/chat/start", data={"industry": "tech"})
    result = response.json()
    
    if result["success"]:
        print(f"   专家: {result['expert_name']}")
        print(f"   欢迎消息: {result['welcome_message'][:100]}...")
        print(f"   初始问题: {result['initial_questions']}")
    
    # 3. 模拟用户回答
    messages = [
        "我想开发一个基于AI的智能客服系统，帮助中小企业提升客户服务效率",
        "我们的核心技术是自然语言处理和机器学习，可以理解客户意图并自动回复",
        "目标客户是中小企业，特别是电商和服务行业，他们需要24小时客服但成本有限",
        "我们计划采用SaaS订阅模式，按月收费，预计6个月内实现盈亏平衡"
    ]
    
    print("\n3. 模拟对话过程...")
    for i, message in enumerate(messages, 1):
        print(f"\n   第{i}轮对话:")
        print(f"   用户: {message}")
        
        response = requests.post(f"{base_url}/chat/message", data={
            "message": message,
            "message_type": "text"
        })
        
        result = response.json()
        if result["success"]:
            print(f"   专家: {result['message'][:150]}...")
            print(f"   进度: {result.get('progress', 0)}%")
            
            if result.get("analysis_report"):
                print("\n🎉 分析报告已生成!")
                report = result["analysis_report"]
                print(f"   报告专家: {report['expert']}")
                print(f"   生成时间: {report['generated_at']}")
                print(f"   报告内容: {report['report_text'][:200]}...")
                break
        
        time.sleep(1)  # 模拟真实对话间隔
    
    # 4. 获取系统统计
    print("\n4. 系统统计信息...")
    response = requests.get(f"{base_url}/system/stats")
    stats = response.json()
    print(f"   系统状态: {'就绪' if stats['is_initialized'] else '初始化中'}")
    print(f"   知识库文档: {stats['vector_store_stats']['total_documents']}")
    
    print("\n✅ 演示完成!")
    print("💡 您可以在浏览器中访问 http://localhost:8000 体验完整功能")

if __name__ == "__main__":
    try:
        demo_conversation()
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保应用已启动:")
        print("   python main.py")
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
