#!/usr/bin/env python3
"""
双模式分析功能演示脚本
演示文档分析和引导式分析两种功能
"""
import requests
import json
import time

def demo_document_analysis():
    """演示文档分析功能"""
    base_url = "http://localhost:8000"
    
    print("📄 文档分析模式演示")
    print("=" * 50)
    
    # 测试文档直接分析
    test_file = "test_files/sample_business_plan.md"
    
    print(f"\n1. 上传并分析商业计划书: {test_file}")
    
    try:
        with open(test_file, 'rb') as f:
            files = {'file': ('sample_business_plan.md', f)}
            response = requests.post(f"{base_url}/upload/analyze-document", files=files)
        
        if response.status_code == 200:
            result = response.json()
            
            if result["success"]:
                print("   ✅ 文档分析成功!")
                
                report = result["analysis_report"]
                print(f"   📊 报告类型: {report['report_type']}")
                print(f"   📁 文件名: {report['file_name']}")
                print(f"   📈 完整性评分: {report['completeness_score']:.1f}%")
                print(f"   📝 分析部分数: {len(report['analysis_sections'])}")
                
                # 显示部分分析内容
                print(f"\n   📋 项目概述预览:")
                overview = report['analysis_sections'].get('project_overview', '')
                print(f"   {overview[:200]}...")
                
                print(f"\n   💡 主要建议:")
                for i, rec in enumerate(report.get('recommendations', [])[:3], 1):
                    print(f"   {i}. {rec}")
                
            else:
                print(f"   ❌ 分析失败: {result.get('error', '未知错误')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 分析异常: {e}")

def demo_guided_analysis():
    """演示引导式分析功能"""
    base_url = "http://localhost:8000"
    
    print("\n\n🤖 引导式分析模式演示")
    print("=" * 50)
    
    # 1. 启动引导式分析
    print("\n1. 启动引导式分析...")
    
    try:
        response = requests.post(f"{base_url}/guided-analysis/start", data={"industry": "tech"})
        
        if response.status_code == 200:
            result = response.json()
            
            if result["success"]:
                print("   ✅ 引导式分析启动成功!")
                print(f"   👨‍💼 专家: {result['expert_name']}")
                print(f"   📊 总阶段数: {result['total_stages']}")
                print(f"   📝 当前阶段: {result['current_stage']}")
                print(f"   ❓ 问题数: {len(result['questions'])}")
                
                # 显示第一个问题
                first_question = result['questions'][0]
                print(f"\n   第一个问题:")
                print(f"   Q: {first_question['question']}")
                print(f"   提示: {first_question['placeholder']}")
                
                # 模拟回答问题
                print(f"\n2. 模拟回答问题...")
                
                sample_answers = [
                    {
                        "question_id": "project_name",
                        "answer": "智能健康管理平台，通过AI技术为用户提供个性化健康建议和疾病预防方案"
                    },
                    {
                        "question_id": "target_customers", 
                        "answer": "健康意识较强的25-45岁中青年群体，特别是白领和慢性病患者"
                    },
                    {
                        "question_id": "core_problem",
                        "answer": "解决个人健康管理缺乏专业指导、健康数据分散、预防意识不足的问题"
                    }
                ]
                
                for i, answer_data in enumerate(sample_answers, 1):
                    print(f"\n   回答问题 {i}:")
                    print(f"   A: {answer_data['answer']}")
                    
                    response = requests.post(f"{base_url}/guided-analysis/answer", data={
                        "answer": answer_data["answer"],
                        "question_id": answer_data["question_id"]
                    })
                    
                    if response.status_code == 200:
                        answer_result = response.json()
                        
                        if answer_result["success"]:
                            print(f"   ✅ 回答已记录")
                            print(f"   💬 专家回复: {answer_result['message'][:100]}...")
                            print(f"   📈 进度: {answer_result.get('progress', 0)}%")
                            
                            if answer_result.get('analysis_completed'):
                                print(f"\n   🎉 分析完成！生成了完整的分析报告")
                                
                                report = answer_result['analysis_report']
                                print(f"   📊 报告类型: {report['report_type']}")
                                print(f"   👨‍💼 分析专家: {report['expert']}")
                                print(f"   🏭 行业: {report['industry']}")
                                print(f"   📝 回答总数: {report['total_responses']}")
                                print(f"   ✅ 完成率: {report['completion_rate']}%")
                                
                                # 显示部分分析内容
                                sections = report['analysis_sections']
                                if 'project_overview' in sections:
                                    print(f"\n   📋 项目概述预览:")
                                    overview = sections['project_overview']
                                    print(f"   {overview[:200]}...")
                                
                                break
                        else:
                            print(f"   ❌ 回答处理失败")
                    else:
                        print(f"   ❌ HTTP错误: {response.status_code}")
                    
                    time.sleep(1)  # 模拟真实对话间隔
                
            else:
                print(f"   ❌ 启动失败: {result.get('error', '未知错误')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 引导式分析异常: {e}")

def demo_comparison():
    """对比两种分析模式"""
    print("\n\n⚖️ 两种分析模式对比")
    print("=" * 50)
    
    comparison_data = [
        ["特性", "文档分析模式", "引导式分析模式"],
        ["输入方式", "上传文档文件", "回答专业问题"],
        ["分析速度", "快速（秒级）", "较慢（需多轮对话）"],
        ["分析深度", "基于现有文档内容", "深入挖掘项目细节"],
        ["个性化程度", "中等", "高（针对性问题）"],
        ["适用场景", "已有完整商业计划书", "项目初期或需要指导"],
        ["专家互动", "无", "有（多轮对话）"],
        ["报告质量", "依赖文档质量", "基于详细问答"],
        ["用户体验", "简单快捷", "引导式、教育性强"]
    ]
    
    # 打印对比表格
    for i, row in enumerate(comparison_data):
        if i == 0:
            print(f"\n   {row[0]:<12} | {row[1]:<20} | {row[2]:<20}")
            print(f"   {'-'*12} | {'-'*20} | {'-'*20}")
        else:
            print(f"   {row[0]:<12} | {row[1]:<20} | {row[2]:<20}")
    
    print(f"\n💡 使用建议:")
    print(f"   • 如果您已经有完整的商业计划书，推荐使用【文档分析模式】")
    print(f"   • 如果您的项目还在初期阶段，推荐使用【引导式分析模式】")
    print(f"   • 两种模式可以结合使用，获得更全面的分析")

def main():
    """主函数"""
    print("🚀 创业分析智能体 - 双模式分析演示")
    print("=" * 60)
    
    try:
        # 演示文档分析
        demo_document_analysis()
        
        # 演示引导式分析
        demo_guided_analysis()
        
        # 对比两种模式
        demo_comparison()
        
        print(f"\n✅ 双模式分析演示完成!")
        print(f"💡 您可以在浏览器中访问 http://localhost:8000 体验完整功能")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保应用已启动:")
        print("   python main.py")
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")

if __name__ == "__main__":
    main()
