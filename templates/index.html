<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创业分析智能体 - U30创业者的专业伙伴</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 sidebar">
                <div class="sidebar-header">
                    <h4><i class="fas fa-rocket"></i> 创业智能体</h4>
                    <p class="text-muted">U30创业者的专业伙伴</p>
                </div>
                
                <!-- 行业选择 -->
                <div class="industry-selection mb-4">
                    <h6>选择您的行业</h6>
                    <div id="industry-list" class="industry-grid">
                        <!-- 行业列表将通过JavaScript动态加载 -->
                    </div>
                </div>
                
                <!-- 分析模式选择 -->
                <div class="analysis-mode mb-4">
                    <h6>选择分析方式</h6>
                    <div class="mode-selection">
                        <div class="mode-option" id="document-mode">
                            <i class="fas fa-file-upload"></i>
                            <h6>文档分析</h6>
                            <p>上传商业计划书，直接生成分析报告</p>
                        </div>
                        <div class="mode-option" id="guided-mode">
                            <i class="fas fa-comments"></i>
                            <h6>引导分析</h6>
                            <p>通过专业问答，深入了解您的项目</p>
                        </div>
                    </div>
                </div>

                <!-- 文件上传区域 -->
                <div class="file-upload mb-4" id="file-upload-section" style="display: none;">
                    <h6>上传创业计划书</h6>
                    <div class="upload-area" id="upload-area">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>拖拽文件到这里或点击上传</p>
                        <small class="text-muted">支持多种格式</small>
                        <input type="file" id="file-input" accept=".pdf,.docx,.doc,.txt,.md,.markdown,.xlsx,.xls,.pptx,.ppt,.json,.csv,.html,.htm" hidden>
                    </div>

                    <!-- 分析选项 -->
                    <div class="analysis-options mt-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="analysisType" id="quickAnalysis" value="quick" checked>
                            <label class="form-check-label" for="quickAnalysis">
                                <strong>快速分析</strong> - 仅解析文档结构和内容
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="analysisType" id="fullAnalysis" value="full">
                            <label class="form-check-label" for="fullAnalysis">
                                <strong>完整分析</strong> - 生成详细的分析报告
                            </label>
                        </div>
                    </div>

                    <!-- 支持格式说明 -->
                    <div class="supported-formats mt-2" id="supported-formats">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            <span id="format-info">加载格式信息中...</span>
                        </small>
                    </div>

                    <!-- 上传进度 -->
                    <div class="upload-progress mt-2" id="upload-progress" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar" id="upload-progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted" id="upload-status">上传中...</small>
                    </div>
                </div>
                
                <!-- 系统状态 -->
                <div class="system-status">
                    <h6>系统状态</h6>
                    <div id="system-stats">
                        <div class="stat-item">
                            <span class="stat-label">知识库文档:</span>
                            <span class="stat-value" id="doc-count">加载中...</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">系统状态:</span>
                            <span class="stat-value" id="system-status">初始化中...</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主聊天区域 -->
            <div class="col-md-9 main-content">
                <!-- 聊天头部 -->
                <div class="chat-header">
                    <div class="expert-info" id="expert-info" style="display: none;">
                        <div class="expert-avatar">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="expert-details">
                            <h5 id="expert-name">专家</h5>
                            <p id="expert-description" class="text-muted">行业专家</p>
                        </div>
                    </div>
                    <div class="progress-bar" id="progress-container" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar bg-success" id="analysis-progress" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">分析进度</small>
                    </div>
                </div>
                
                <!-- 聊天消息区域 -->
                <div class="chat-messages" id="chat-messages">
                    <div class="welcome-screen" id="welcome-screen">
                        <div class="text-center">
                            <i class="fas fa-robot fa-3x text-primary mb-3"></i>
                            <h3>欢迎使用创业分析智能体</h3>
                            <p class="lead">我是您的专业创业顾问，为U30创业者提供个性化的分析和建议</p>
                            <div class="features">
                                <div class="row">
                                    <div class="col-md-4">
                                        <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                                        <h6>市场分析</h6>
                                        <p class="small">深度分析市场机会和竞争格局</p>
                                    </div>
                                    <div class="col-md-4">
                                        <i class="fas fa-lightbulb fa-2x text-warning mb-2"></i>
                                        <h6>商业模式</h6>
                                        <p class="small">优化您的商业模式和盈利策略</p>
                                    </div>
                                    <div class="col-md-4">
                                        <i class="fas fa-shield-alt fa-2x text-info mb-2"></i>
                                        <h6>风险评估</h6>
                                        <p class="small">识别潜在风险并提供应对方案</p>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-4">请先选择您的行业，然后开始我们的对话吧！</p>
                        </div>
                    </div>
                </div>
                
                <!-- 输入区域 -->
                <div class="chat-input" id="chat-input" style="display: none;">
                    <div class="input-group">
                        <input type="text" class="form-control" id="message-input" placeholder="输入您的消息..." disabled>
                        <button class="btn btn-primary" id="send-button" disabled>
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    
                    <!-- 快速问题 -->
                    <div class="quick-questions" id="quick-questions" style="display: none;">
                        <small class="text-muted">快速问题:</small>
                        <div class="question-buttons" id="question-buttons">
                            <!-- 快速问题按钮将动态生成 -->
                        </div>
                    </div>

                    <!-- 引导式分析问题区域 -->
                    <div class="guided-questions" id="guided-questions" style="display: none;">
                        <div class="current-question mb-3">
                            <h6 id="current-question-title">问题</h6>
                            <p id="current-question-text"></p>
                            <textarea class="form-control" id="guided-answer" rows="3" placeholder="请详细回答..."></textarea>
                            <small class="text-muted" id="question-hint"></small>
                        </div>
                        <div class="question-actions">
                            <button class="btn btn-primary" id="submit-answer">
                                <i class="fas fa-arrow-right"></i> 提交答案
                            </button>
                            <button class="btn btn-outline-secondary" id="skip-question" style="display: none;">
                                跳过此题
                            </button>
                        </div>
                        <div class="stage-progress mt-3">
                            <small class="text-muted">当前阶段: <span id="current-stage-name"></span></small>
                            <div class="progress mt-1">
                                <div class="progress-bar bg-info" id="stage-progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 分析报告模态框 -->
    <div class="modal fade" id="reportModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创业分析报告</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="report-content">
                    <!-- 报告内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="download-report">下载报告</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 文件分析结果模态框 -->
    <div class="modal fade" id="fileAnalysisModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-file-alt"></i> 文档分析结果
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 文件基本信息 -->
                    <div class="file-info mb-4">
                        <h6><i class="fas fa-info-circle"></i> 文件信息</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div id="file-basic-info">
                                    <!-- 基本信息将通过JavaScript填充 -->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div id="file-metadata">
                                    <!-- 元数据将通过JavaScript填充 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分析摘要 -->
                    <div class="analysis-summary mb-4">
                        <h6><i class="fas fa-chart-bar"></i> 分析摘要</h6>
                        <div id="analysis-summary-content" class="alert alert-info">
                            <!-- 分析摘要内容 -->
                        </div>
                    </div>

                    <!-- 创业计划书结构分析 -->
                    <div class="business-plan-analysis mb-4" id="business-plan-analysis" style="display: none;">
                        <h6><i class="fas fa-sitemap"></i> 创业计划书结构分析</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div id="plan-sections">
                                    <!-- 计划书各部分 -->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div id="validation-results">
                                    <!-- 验证结果 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 内容预览 -->
                    <div class="content-preview">
                        <h6><i class="fas fa-eye"></i> 内容预览</h6>
                        <div class="content-preview-box">
                            <pre id="content-preview-text"></pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-success" id="use-document-content">
                        <i class="fas fa-check"></i> 使用此文档内容
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
</body>
</html>
