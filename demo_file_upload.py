#!/usr/bin/env python3
"""
文件上传功能演示脚本
"""
import requests
import json
import os

def demo_file_upload():
    """演示文件上传功能"""
    base_url = "http://localhost:8000"
    
    print("📁 文件上传功能演示")
    print("=" * 60)
    
    # 1. 获取支持的文件格式
    print("\n1. 获取支持的文件格式...")
    response = requests.get(f"{base_url}/upload/supported-formats")
    formats_data = response.json()
    
    print("   支持的文件格式:")
    for mime_type, info in formats_data["supported_types"].items():
        status = "✅" if info["available"] else "❌"
        extensions = ", ".join(info["extensions"])
        print(f"   {status} {info['name']} ({extensions}) - {info['description']}")
    
    print(f"\n   依赖库状态:")
    for lib, status in formats_data["dependencies_status"].items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {lib}")
    
    # 2. 测试不同格式的文件上传
    test_files = [
        ("test_files/sample_business_plan.md", "Markdown创业计划书"),
        ("test_files/sample_data.json", "JSON数据文件"),
        ("test_files/sample_text.txt", "文本文件")
    ]
    
    print(f"\n2. 测试文件上传和解析...")
    
    for file_path, description in test_files:
        if not os.path.exists(file_path):
            print(f"   ⚠️ 文件不存在: {file_path}")
            continue
        
        print(f"\n   📄 上传 {description}...")
        
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f)}
                response = requests.post(f"{base_url}/upload/business-plan", files=files)
            
            if response.status_code == 200:
                result = response.json()
                
                if result["success"]:
                    print(f"   ✅ 上传成功!")
                    print(f"      文件类型: {result['metadata']['format']}")
                    print(f"      文件大小: {format_file_size(result['file_info']['size'])}")
                    
                    # 显示解析结果
                    if result.get('business_plan_sections'):
                        sections = result['business_plan_sections']
                        filled_sections = sum(1 for content in sections.values() if content.strip())
                        total_sections = len(sections)
                        print(f"      创业计划书完整性: {filled_sections}/{total_sections} 部分")
                    
                    # 显示分析摘要
                    if result.get('analysis_summary'):
                        print(f"      分析摘要:")
                        for line in result['analysis_summary'].split('\n')[:3]:
                            if line.strip():
                                print(f"        {line}")
                    
                    print(f"      内容预览: {result['content_preview'][:100]}...")
                    
                else:
                    print(f"   ❌ 上传失败: {result.get('error', '未知错误')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 上传异常: {e}")
    
    # 3. 测试不支持的文件格式
    print(f"\n3. 测试不支持的文件格式...")
    
    # 创建一个临时的不支持格式文件
    unsupported_file = "test_files/unsupported.xyz"
    try:
        with open(unsupported_file, 'w') as f:
            f.write("这是一个不支持的文件格式")
        
        with open(unsupported_file, 'rb') as f:
            files = {'file': ('unsupported.xyz', f)}
            response = requests.post(f"{base_url}/upload/business-plan", files=files)
        
        if response.status_code != 200:
            print(f"   ✅ 正确拒绝了不支持的文件格式 (HTTP {response.status_code})")
        else:
            result = response.json()
            if not result["success"]:
                print(f"   ✅ 正确拒绝了不支持的文件格式: {result.get('error', '')}")
        
        # 清理临时文件
        os.remove(unsupported_file)
        
    except Exception as e:
        print(f"   ⚠️ 测试异常: {e}")
    
    # 4. 获取系统统计
    print(f"\n4. 系统统计信息...")
    response = requests.get(f"{base_url}/system/stats")
    stats = response.json()
    
    print(f"   📊 系统状态: {'就绪' if stats['is_initialized'] else '初始化中'}")
    print(f"   📚 知识库文档: {stats['vector_store_stats']['total_documents']}")
    print(f"   📁 已上传文件: {stats['upload_stats']['total_files']}")
    print(f"   🔧 支持格式数: {stats['supported_formats']}")
    
    print(f"\n✅ 文件上传功能演示完成!")
    print(f"💡 您可以在浏览器中访问 http://localhost:8000 体验完整的文件上传功能")

def format_file_size(bytes_size):
    """格式化文件大小"""
    if bytes_size < 1024:
        return f"{bytes_size} B"
    elif bytes_size < 1024 * 1024:
        return f"{bytes_size / 1024:.1f} KB"
    else:
        return f"{bytes_size / (1024 * 1024):.1f} MB"

if __name__ == "__main__":
    try:
        demo_file_upload()
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保应用已启动:")
        print("   python main.py")
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
